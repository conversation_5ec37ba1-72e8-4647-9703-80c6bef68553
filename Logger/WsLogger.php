<?php

namespace Alienor\LoggerWebserviceBundle\Logger;

/**
 * Logger ws
 *
 */
class WsLogger
{
    private array $queries = array();

    /**
     * Begins a log for a query. Must be called before the query.
     *
     */
    public function logQueryStart(): int
    {
        $numLog = count($this->queries);
        $this->queries[$numLog] = array('time' => microtime(true));

        return $numLog;
    }

    /**
     * Closes the log begun with logQueryStart(). Must be called after the query.
     *
     */
    public function logQueryStop($numLog, $url, $params): void
    {
        $time = microtime(true) - $this->queries[$numLog]['time'];

        $this->queries[$numLog] = array(
            'url' => $url,
            'time' => $time,
            'params' => $params,
        );
    }

    public function getQueries(): array
    {
        return $this->queries;
    }
}
