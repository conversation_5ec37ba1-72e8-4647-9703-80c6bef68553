parameters:
  ### CONFIGURATION WEBSERVICES ###
  client:                                   "Enseigne"

  ### CONFIGURATION PASSBOOK ###
  sel:                                      zefidbyaquitem
  id_enseigne:                              zefid
  carte.passbook:                           true
  passbook.enseigneId:                      470000000001
  passbook.saltClient:                      '%env(salt_client)%'
  passbook.url:                             '%env(passbook_url)%'

  ### CONFIGURATION DONUTS ###
  carte.donuts: 		                    true

  # Définie si le site est responsive. 
  # -> Si false, pensez à fixer la taille du container
  template.isResponsive:                    true
  template.menuLateral:                     true
  template.footer:                          true

  # CARTE
  carte.telechargement:	                    true

  # ACTIVATION DE CARTE
  activation.carte:                         true

  # LOGO
  logoClient:                               "/logo/logo_zefid.png"

  # BIENVENUE
  remise.bienvenue.pourcentageAllow:        true

  # ANNIVERSAIRE
  remise.anniversaire.pourcentageAllow:     true

  # FIDELITE
  remise.fidelite.pourcentageAllow:         true

  # RESEAU SOCIAUX
  # mettre à false pour masquer le logo/lien
  reseau.page.facebook:                     "https://www.facebook.com/groupeaquitem/?fref=ts"
  reseau.page.twitter:                      "https://twitter.com/Groupe_Aquitem"


  # GEOLOCALISATION DU CLIENT
  adresse.geolocalisation: false
  alienor_requeteur_adresse.wsUrl: http://aqui-symfony.alienor.net/getAdresse
  alienor_requeteur_adresse.client: testalienor
  alienor_requeteur_adresse.cle: CLEALIENOREREQUETEURADRESSE
