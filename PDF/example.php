<?php
require_once('fpdi.php');

// initiate FPDI
$pdf =& new FPDI('P', 'mm', array(100, 100));

// add a page
$pdf->AddPage();
// set the sourcefile
$pdf->setSourceFile('10x10.pdf');
// import page 1
$tplIdx = $pdf->importPage(1);
// use the imported page and place it at point 10,10 with a width of 100 mm
$pdf->useTemplate($tplIdx);

$pdf->Image('google.png', $pdf->w/2, $pdf->h/2);

$pdf->Output('newpdf.pdf', 'F');
