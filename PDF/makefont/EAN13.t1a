%!PS-AdobeFont-1.0: CodeEAN13 Grandzebu. 2003. All Rights Reserved - GNU General Public License
%%CreationDate: Fri Aug 19 14:19:09 2011
% Converted by ttf2pt1 3.4.4/ttf
% Args: -a EAN13.ttf EAN13 
%%EndComments
12 dict begin
/FontInfo 9 dict dup begin
/version (1,20 March 3, 2004) readonly def
/Notice (Grandzebu. 2003. All Rights Reserved - GNU General Public License) readonly def
/FullName (Code EAN13) readonly def
/FamilyName (Code EAN13) readonly def
/Weight (Regular) readonly def
/ItalicAngle 0.000000 def
/isFixedPitch false def
/UnderlinePosition -27 def
/UnderlineThickness 24 def
end readonly def
/FontName /CodeEAN13 def
/PaintType 0 def
/StrokeWidth 0 def
/FontType 1 def
/FontMatrix [0.001 0 0 0.001 0 0] def
/FontBBox {0 -245 342 879} readonly def
/Encoding 256 array
dup 0 /.notdef put
dup 1 /.notdef put
dup 2 /.notdef put
dup 3 /.notdef put
dup 4 /.notdef put
dup 5 /.notdef put
dup 6 /.notdef put
dup 7 /.notdef put
dup 8 /.notdef put
dup 9 /.notdef put
dup 10 /.notdef put
dup 11 /.notdef put
dup 12 /.notdef put
dup 13 /.notdef put
dup 14 /.notdef put
dup 15 /.notdef put
dup 16 /.notdef put
dup 17 /.notdef put
dup 18 /.notdef put
dup 19 /.notdef put
dup 20 /.notdef put
dup 21 /.notdef put
dup 22 /.notdef put
dup 23 /.notdef put
dup 24 /.notdef put
dup 25 /.notdef put
dup 26 /.notdef put
dup 27 /.notdef put
dup 28 /.notdef put
dup 29 /.notdef put
dup 30 /.notdef put
dup 31 /.notdef put
dup 32 /space put
dup 33 /.notdef put
dup 34 /.notdef put
dup 35 /.notdef put
dup 36 /.notdef put
dup 37 /.notdef put
dup 38 /.notdef put
dup 39 /.notdef put
dup 40 /.notdef put
dup 41 /.notdef put
dup 42 /asterisk put
dup 43 /plus put
dup 44 /.notdef put
dup 45 /.notdef put
dup 46 /.notdef put
dup 47 /.notdef put
dup 48 /zero put
dup 49 /one put
dup 50 /two put
dup 51 /three put
dup 52 /four put
dup 53 /five put
dup 54 /six put
dup 55 /seven put
dup 56 /eight put
dup 57 /nine put
dup 58 /colon put
dup 59 /.notdef put
dup 60 /.notdef put
dup 61 /.notdef put
dup 62 /.notdef put
dup 63 /.notdef put
dup 64 /.notdef put
dup 65 /A put
dup 66 /B put
dup 67 /C put
dup 68 /D put
dup 69 /E put
dup 70 /F put
dup 71 /G put
dup 72 /H put
dup 73 /I put
dup 74 /J put
dup 75 /K put
dup 76 /L put
dup 77 /M put
dup 78 /N put
dup 79 /O put
dup 80 /P put
dup 81 /Q put
dup 82 /R put
dup 83 /S put
dup 84 /T put
dup 85 /.notdef put
dup 86 /.notdef put
dup 87 /.notdef put
dup 88 /.notdef put
dup 89 /.notdef put
dup 90 /.notdef put
dup 91 /bracketleft put
dup 92 /backslash put
dup 93 /.notdef put
dup 94 /.notdef put
dup 95 /.notdef put
dup 96 /.notdef put
dup 97 /a put
dup 98 /b put
dup 99 /c put
dup 100 /d put
dup 101 /e put
dup 102 /f put
dup 103 /g put
dup 104 /h put
dup 105 /i put
dup 106 /j put
dup 107 /k put
dup 108 /l put
dup 109 /m put
dup 110 /n put
dup 111 /o put
dup 112 /p put
dup 113 /q put
dup 114 /r put
dup 115 /s put
dup 116 /t put
dup 117 /.notdef put
dup 118 /.notdef put
dup 119 /.notdef put
dup 120 /.notdef put
dup 121 /.notdef put
dup 122 /.notdef put
dup 123 /.notdef put
dup 124 /.notdef put
dup 125 /.notdef put
dup 126 /.notdef put
dup 127 /.notdef put
dup 128 /.notdef put
dup 129 /.notdef put
dup 130 /.notdef put
dup 131 /.notdef put
dup 132 /.notdef put
dup 133 /.notdef put
dup 134 /.notdef put
dup 135 /.notdef put
dup 136 /.notdef put
dup 137 /.notdef put
dup 138 /.notdef put
dup 139 /.notdef put
dup 140 /.notdef put
dup 141 /.notdef put
dup 142 /.notdef put
dup 143 /.notdef put
dup 144 /.notdef put
dup 145 /.notdef put
dup 146 /.notdef put
dup 147 /.notdef put
dup 148 /.notdef put
dup 149 /.notdef put
dup 150 /.notdef put
dup 151 /.notdef put
dup 152 /.notdef put
dup 153 /.notdef put
dup 154 /.notdef put
dup 155 /.notdef put
dup 156 /.notdef put
dup 157 /.notdef put
dup 158 /.notdef put
dup 159 /.notdef put
dup 160 /.notdef put
dup 161 /.notdef put
dup 162 /.notdef put
dup 163 /.notdef put
dup 164 /.notdef put
dup 165 /.notdef put
dup 166 /.notdef put
dup 167 /.notdef put
dup 168 /.notdef put
dup 169 /.notdef put
dup 170 /.notdef put
dup 171 /.notdef put
dup 172 /.notdef put
dup 173 /.notdef put
dup 174 /.notdef put
dup 175 /.notdef put
dup 176 /.notdef put
dup 177 /.notdef put
dup 178 /.notdef put
dup 179 /.notdef put
dup 180 /.notdef put
dup 181 /.notdef put
dup 182 /.notdef put
dup 183 /.notdef put
dup 184 /.notdef put
dup 185 /.notdef put
dup 186 /.notdef put
dup 187 /.notdef put
dup 188 /.notdef put
dup 189 /.notdef put
dup 190 /.notdef put
dup 191 /.notdef put
dup 192 /.notdef put
dup 193 /.notdef put
dup 194 /.notdef put
dup 195 /.notdef put
dup 196 /.notdef put
dup 197 /.notdef put
dup 198 /.notdef put
dup 199 /.notdef put
dup 200 /.notdef put
dup 201 /.notdef put
dup 202 /.notdef put
dup 203 /.notdef put
dup 204 /.notdef put
dup 205 /.notdef put
dup 206 /.notdef put
dup 207 /.notdef put
dup 208 /.notdef put
dup 209 /.notdef put
dup 210 /.notdef put
dup 211 /.notdef put
dup 212 /.notdef put
dup 213 /.notdef put
dup 214 /.notdef put
dup 215 /.notdef put
dup 216 /.notdef put
dup 217 /.notdef put
dup 218 /.notdef put
dup 219 /.notdef put
dup 220 /.notdef put
dup 221 /.notdef put
dup 222 /.notdef put
dup 223 /.notdef put
dup 224 /.notdef put
dup 225 /.notdef put
dup 226 /.notdef put
dup 227 /.notdef put
dup 228 /.notdef put
dup 229 /.notdef put
dup 230 /.notdef put
dup 231 /.notdef put
dup 232 /.notdef put
dup 233 /.notdef put
dup 234 /.notdef put
dup 235 /.notdef put
dup 236 /.notdef put
dup 237 /.notdef put
dup 238 /.notdef put
dup 239 /.notdef put
dup 240 /.notdef put
dup 241 /.notdef put
dup 242 /.notdef put
dup 243 /.notdef put
dup 244 /.notdef put
dup 245 /.notdef put
dup 246 /.notdef put
dup 247 /.notdef put
dup 248 /.notdef put
dup 249 /.notdef put
dup 250 /.notdef put
dup 251 /.notdef put
dup 252 /.notdef put
dup 253 /.notdef put
dup 254 /.notdef put
dup 255 /.notdef put
readonly def
currentdict end
currentfile eexec
dup /Private 16 dict dup begin
/RD{string currentfile exch readstring pop}executeonly def
/ND{noaccess def}executeonly def
/NP{noaccess put}executeonly def
/ForceBold false def
/BlueValues [ -245 -243 879 879 -50 -49 ] def
/OtherBlues [ -122 -122 0 0 ] def
/StdHW [ 25 ] def
/StdVW [ 25 ] def
/StemSnapH [ 25 ] def
/StemSnapV [ 25 ] def
/MinFeature {16 16} def
/password 5839 def
/Subrs 176 array
dup 0 {
	3 0 callothersubr pop pop setcurrentpoint return
	} NP
dup 1 {
	0 1 callothersubr return
	} NP
dup 2 {
	0 2 callothersubr return
	} NP
dup 3 {
	return
	} NP
dup 4 {
	1 3 callothersubr pop callsubr return
	} NP
dup 5 {
	167 23 vstem
	-244 24 hstem
	73 23 vstem
	-72 23 hstem
	return
	} NP
dup 6 {
	269 24 vstem
	-122 1001 hstem
	317 25 vstem
	return
	} NP
dup 7 {
	147 24 vstem
	-244 195 hstem
	return
	} NP
dup 8 {
	-121 23 hstem
	return
	} NP
dup 9 {
	155 16 vstem
	-244 195 hstem
	return
	} NP
dup 10 {
	147 24 vstem
	-244 195 hstem
	return
	} NP
dup 11 {
	269 24 vstem
	-122 1001 hstem
	317 25 vstem
	return
	} NP
dup 12 {
	-244 24 hstem
	170 25 vstem
	-73 24 hstem
	return
	} NP
dup 13 {
	86 109 vstem
	-73 24 hstem
	return
	} NP
dup 14 {
	170 25 vstem
	-244 24 hstem
	269 24 vstem
	-122 1001 hstem
	317 25 vstem
	return
	} NP
dup 15 {
	160 23 vstem
	-244 25 hstem
	-72 23 hstem
	return
	} NP
dup 16 {
	70 105 vstem
	-72 23 hstem
	return
	} NP
dup 17 {
	269 24 vstem
	-122 1001 hstem
	317 25 vstem
	return
	} NP
dup 18 {
	-213 23 hstem
	153 24 vstem
	return
	} NP
dup 19 {
	-244 99 hstem
	153 24 vstem
	return
	} NP
dup 20 {
	153 24 vstem
	-213 23 hstem
	65 27 vstem
	-69 20 hstem
	return
	} NP
dup 21 {
	153 23 vstem
	-213 23 hstem
	return
	} NP
dup 22 {
	65 139 vstem
	269 24 vstem
	-122 1001 hstem
	317 25 vstem
	return
	} NP
dup 23 {
	169 23 vstem
	-244 24 hstem
	return
	} NP
dup 24 {
	73 119 vstem
	-244 24 hstem
	-147 98 hstem
	return
	} NP
dup 25 {
	169 23 vstem
	-131 24 hstem
	77 23 vstem
	-72 23 hstem
	return
	} NP
dup 26 {
	77 101 vstem
	-72 23 hstem
	return
	} NP
dup 27 {
	269 24 vstem
	-122 1001 hstem
	317 25 vstem
	return
	} NP
dup 28 {
	172 29 vstem
	-244 23 hstem
	73 19 vstem
	-134 85 hstem
	return
	} NP
dup 29 {
	-134 23 hstem
	172 29 vstem
	73 19 vstem
	-244 23 hstem
	return
	} NP
dup 30 {
	269 24 vstem
	-122 1001 hstem
	317 25 vstem
	return
	} NP
dup 31 {
	73 128 vstem
	-73 23 hstem
	return
	} NP
dup 32 {
	73 128 vstem
	-68 18 hstem
	return
	} NP
dup 33 {
	101 24 vstem
	-244 20 hstem
	-73 23 hstem
	return
	} NP
dup 34 {
	73 128 vstem
	-73 23 hstem
	return
	} NP
dup 35 {
	269 24 vstem
	-122 1001 hstem
	317 25 vstem
	return
	} NP
dup 36 {
	110 53 vstem
	81 24 vstem
	-68 19 hstem
	167 24 vstem
	return
	} NP
dup 37 {
	175 24 vstem
	-244 19 hstem
	73 24 vstem
	-147 19 hstem
	269 24 vstem
	-122 1001 hstem
	317 25 vstem
	return
	} NP
dup 38 {
	81 24 vstem
	-147 19 hstem
	167 24 vstem
	-68 19 hstem
	return
	} NP
dup 39 {
	-245 86 hstem
	return
	} NP
dup 40 {
	167 35 vstem
	-174 125 hstem
	return
	} NP
dup 41 {
	-182 23 hstem
	73 23 vstem
	-72 23 hstem
	177 25 vstem
	return
	} NP
dup 42 {
	269 24 vstem
	-122 1001 hstem
	317 25 vstem
	return
	} NP
dup 43 {
	73 49 vstem
	0 879 hstem
	146 25 vstem
	return
	} NP
dup 44 {
	124 22 vstem
	-244 24 hstem
	30 23 vstem
	-72 23 hstem
	return
	} NP
dup 45 {
	49 49 vstem
	0 879 hstem
	146 25 vstem
	100 24 vstem
	-244 195 hstem
	return
	} NP
dup 46 {
	-121 23 hstem
	return
	} NP
dup 47 {
	108 16 vstem
	-244 195 hstem
	return
	} NP
dup 48 {
	100 24 vstem
	-244 195 hstem
	return
	} NP
dup 49 {
	122 49 vstem
	0 879 hstem
	49 24 vstem
	-244 24 hstem
	return
	} NP
dup 50 {
	122 24 vstem
	-73 24 hstem
	-244 24 hstem
	return
	} NP
dup 51 {
	38 108 vstem
	-73 24 hstem
	return
	} NP
dup 52 {
	24 99 vstem
	0 879 hstem
	146 25 vstem
	return
	} NP
dup 53 {
	124 22 vstem
	-244 25 hstem
	-73 24 hstem
	return
	} NP
dup 54 {
	34 105 vstem
	-73 24 hstem
	return
	} NP
dup 55 {
	122 49 vstem
	0 879 hstem
	24 25 vstem
	-213 24 hstem
	return
	} NP
dup 56 {
	106 23 vstem
	-213 24 hstem
	return
	} NP
dup 57 {
	-244 99 hstem
	106 23 vstem
	return
	} NP
dup 58 {
	18 139 vstem
	-69 20 hstem
	-213 24 hstem
	return
	} NP
dup 59 {
	105 24 vstem
	-213 24 hstem
	return
	} NP
dup 60 {
	24 49 vstem
	0 879 hstem
	146 25 vstem
	return
	} NP
dup 61 {
	124 22 vstem
	-244 24 hstem
	-131 24 hstem
	32 23 vstem
	-72 23 hstem
	return
	} NP
dup 62 {
	28 118 vstem
	-244 24 hstem
	-147 98 hstem
	return
	} NP
dup 63 {
	32 100 vstem
	-72 23 hstem
	return
	} NP
dup 64 {
	24 25 vstem
	0 879 hstem
	73 98 vstem
	return
	} NP
dup 65 {
	126 23 vstem
	-243 23 hstem
	21 25 vstem
	-133 85 hstem
	return
	} NP
dup 66 {
	-133 23 hstem
	126 23 vstem
	21 25 vstem
	-243 23 hstem
	return
	} NP
dup 67 {
	122 49 vstem
	0 879 hstem
	24 74 vstem
	return
	} NP
dup 68 {
	22 127 vstem
	-72 23 hstem
	return
	} NP
dup 69 {
	22 127 vstem
	-67 18 hstem
	return
	} NP
dup 70 {
	50 24 vstem
	-243 20 hstem
	-72 23 hstem
	return
	} NP
dup 71 {
	24 49 vstem
	0 879 hstem
	98 73 vstem
	return
	} NP
dup 72 {
	57 53 vstem
	28 24 vstem
	-68 19 hstem
	115 24 vstem
	-147 19 hstem
	return
	} NP
dup 73 {
	123 23 vstem
	-244 19 hstem
	21 23 vstem
	-147 19 hstem
	return
	} NP
dup 74 {
	122 49 vstem
	0 879 hstem
	73 25 vstem
	-244 86 hstem
	return
	} NP
dup 75 {
	119 34 vstem
	-173 125 hstem
	return
	} NP
dup 76 {
	-182 24 hstem
	24 23 vstem
	-72 24 hstem
	128 25 vstem
	return
	} NP
dup 77 {
	98 73 vstem
	0 879 hstem
	26 24 vstem
	return
	} NP
dup 78 {
	124 22 vstem
	-244 24 hstem
	30 23 vstem
	-72 23 hstem
	return
	} NP
dup 79 {
	24 49 vstem
	0 879 hstem
	100 24 vstem
	-244 195 hstem
	return
	} NP
dup 80 {
	-121 23 hstem
	122 49 vstem
	0 879 hstem
	return
	} NP
dup 81 {
	108 16 vstem
	-244 195 hstem
	return
	} NP
dup 82 {
	100 24 vstem
	-244 195 hstem
	return
	} NP
dup 83 {
	122 49 vstem
	0 879 hstem
	-244 24 hstem
	return
	} NP
dup 84 {
	122 24 vstem
	-73 24 hstem
	-244 24 hstem
	49 49 vstem
	0 879 hstem
	return
	} NP
dup 85 {
	38 108 vstem
	-73 24 hstem
	return
	} NP
dup 86 {
	24 25 vstem
	0 879 hstem
	124 22 vstem
	-244 25 hstem
	-73 24 hstem
	return
	} NP
dup 87 {
	34 105 vstem
	-73 24 hstem
	146 25 vstem
	0 879 hstem
	return
	} NP
dup 88 {
	146 25 vstem
	0 879 hstem
	49 75 vstem
	-213 24 hstem
	return
	} NP
dup 89 {
	106 23 vstem
	-213 24 hstem
	return
	} NP
dup 90 {
	-244 99 hstem
	106 23 vstem
	return
	} NP
dup 91 {
	18 139 vstem
	-69 20 hstem
	-213 24 hstem
	return
	} NP
dup 92 {
	105 24 vstem
	-213 24 hstem
	return
	} NP
dup 93 {
	24 74 vstem
	0 879 hstem
	146 25 vstem
	return
	} NP
dup 94 {
	124 22 vstem
	-244 24 hstem
	-131 24 hstem
	32 23 vstem
	-72 23 hstem
	return
	} NP
dup 95 {
	28 118 vstem
	-244 24 hstem
	-147 98 hstem
	return
	} NP
dup 96 {
	32 100 vstem
	-72 23 hstem
	return
	} NP
dup 97 {
	98 24 vstem
	0 879 hstem
	126 23 vstem
	-243 23 hstem
	21 25 vstem
	-133 85 hstem
	return
	} NP
dup 98 {
	-133 23 hstem
	126 23 vstem
	21 25 vstem
	-243 23 hstem
	return
	} NP
dup 99 {
	146 25 vstem
	0 879 hstem
	return
	} NP
dup 100 {
	146 25 vstem
	0 879 hstem
	return
	} NP
dup 101 {
	22 127 vstem
	-72 23 hstem
	return
	} NP
dup 102 {
	22 127 vstem
	-67 18 hstem
	return
	} NP
dup 103 {
	50 24 vstem
	-243 20 hstem
	-72 23 hstem
	return
	} NP
dup 104 {
	49 24 vstem
	0 879 hstem
	return
	} NP
dup 105 {
	146 25 vstem
	0 879 hstem
	57 53 vstem
	28 24 vstem
	-68 19 hstem
	115 24 vstem
	return
	} NP
dup 106 {
	123 23 vstem
	-244 19 hstem
	21 23 vstem
	-147 19 hstem
	73 25 vstem
	0 879 hstem
	return
	} NP
dup 107 {
	28 24 vstem
	-147 19 hstem
	115 24 vstem
	-68 19 hstem
	return
	} NP
dup 108 {
	98 72 vstem
	0 879 hstem
	49 24 vstem
	-244 86 hstem
	return
	} NP
dup 109 {
	119 34 vstem
	-173 125 hstem
	return
	} NP
dup 110 {
	-182 24 hstem
	24 23 vstem
	-72 24 hstem
	128 25 vstem
	return
	} NP
dup 111 {
	0 73 vstem
	0 879 hstem
	122 24 vstem
	return
	} NP
dup 112 {
	124 22 vstem
	-244 24 hstem
	30 23 vstem
	-72 23 hstem
	return
	} NP
dup 113 {
	0 49 vstem
	0 879 hstem
	100 24 vstem
	-244 195 hstem
	return
	} NP
dup 114 {
	-121 23 hstem
	98 48 vstem
	0 879 hstem
	return
	} NP
dup 115 {
	108 16 vstem
	-244 195 hstem
	return
	} NP
dup 116 {
	100 24 vstem
	-244 195 hstem
	return
	} NP
dup 117 {
	0 49 vstem
	0 879 hstem
	-244 24 hstem
	122 24 vstem
	-73 24 hstem
	return
	} NP
dup 118 {
	38 108 vstem
	-73 24 hstem
	return
	} NP
dup 119 {
	122 24 vstem
	-244 24 hstem
	return
	} NP
dup 120 {
	73 49 vstem
	0 879 hstem
	return
	} NP
dup 121 {
	0 24 vstem
	0 879 hstem
	124 22 vstem
	-244 25 hstem
	-73 24 hstem
	return
	} NP
dup 122 {
	34 105 vstem
	-73 24 hstem
	return
	} NP
dup 123 {
	122 24 vstem
	0 879 hstem
	return
	} NP
dup 124 {
	0 24 vstem
	0 879 hstem
	49 75 vstem
	-213 24 hstem
	return
	} NP
dup 125 {
	106 23 vstem
	-213 24 hstem
	return
	} NP
dup 126 {
	-244 99 hstem
	106 23 vstem
	return
	} NP
dup 127 {
	18 139 vstem
	-69 20 hstem
	-213 24 hstem
	return
	} NP
dup 128 {
	105 24 vstem
	-213 24 hstem
	return
	} NP
dup 129 {
	73 73 vstem
	0 879 hstem
	0 24 vstem
	return
	} NP
dup 130 {
	124 22 vstem
	-244 24 hstem
	-131 24 hstem
	32 23 vstem
	-72 23 hstem
	return
	} NP
dup 131 {
	28 118 vstem
	-244 24 hstem
	-147 98 hstem
	return
	} NP
dup 132 {
	32 100 vstem
	-72 23 hstem
	return
	} NP
dup 133 {
	0 24 vstem
	0 879 hstem
	126 23 vstem
	-243 23 hstem
	return
	} NP
dup 134 {
	21 25 vstem
	-133 85 hstem
	return
	} NP
dup 135 {
	-133 23 hstem
	126 23 vstem
	21 25 vstem
	-243 23 hstem
	49 24 vstem
	0 879 hstem
	return
	} NP
dup 136 {
	98 24 vstem
	0 879 hstem
	return
	} NP
dup 137 {
	22 127 vstem
	-72 23 hstem
	return
	} NP
dup 138 {
	22 127 vstem
	-67 18 hstem
	return
	} NP
dup 139 {
	50 24 vstem
	-243 20 hstem
	-72 23 hstem
	0 24 vstem
	0 879 hstem
	return
	} NP
dup 140 {
	73 25 vstem
	0 879 hstem
	return
	} NP
dup 141 {
	57 53 vstem
	28 24 vstem
	-68 19 hstem
	115 24 vstem
	-147 19 hstem
	0 24 vstem
	0 879 hstem
	return
	} NP
dup 142 {
	123 23 vstem
	-244 19 hstem
	21 23 vstem
	-147 19 hstem
	return
	} NP
dup 143 {
	0 73 vstem
	0 879 hstem
	98 24 vstem
	-244 86 hstem
	return
	} NP
dup 144 {
	119 34 vstem
	-173 125 hstem
	return
	} NP
dup 145 {
	-182 24 hstem
	24 23 vstem
	-72 24 hstem
	128 25 vstem
	return
	} NP
dup 146 {
	147 24 vstem
	-244 195 hstem
	return
	} NP
dup 147 {
	-121 23 hstem
	return
	} NP
dup 148 {
	155 16 vstem
	-244 195 hstem
	return
	} NP
dup 149 {
	147 24 vstem
	-244 195 hstem
	return
	} NP
dup 150 {
	-244 24 hstem
	170 25 vstem
	-73 24 hstem
	return
	} NP
dup 151 {
	86 109 vstem
	-73 24 hstem
	return
	} NP
dup 152 {
	170 25 vstem
	-244 24 hstem
	return
	} NP
dup 153 {
	160 23 vstem
	-244 25 hstem
	-72 23 hstem
	return
	} NP
dup 154 {
	70 105 vstem
	-72 23 hstem
	return
	} NP
dup 155 {
	-213 23 hstem
	153 24 vstem
	return
	} NP
dup 156 {
	-244 99 hstem
	153 24 vstem
	return
	} NP
dup 157 {
	153 24 vstem
	-213 23 hstem
	65 27 vstem
	-69 20 hstem
	return
	} NP
dup 158 {
	153 23 vstem
	-213 23 hstem
	return
	} NP
dup 159 {
	65 139 vstem
	return
	} NP
dup 160 {
	169 23 vstem
	-244 24 hstem
	return
	} NP
dup 161 {
	73 119 vstem
	-244 24 hstem
	-147 98 hstem
	return
	} NP
dup 162 {
	169 23 vstem
	-131 24 hstem
	77 23 vstem
	-72 23 hstem
	return
	} NP
dup 163 {
	77 101 vstem
	-72 23 hstem
	return
	} NP
dup 164 {
	172 29 vstem
	-244 23 hstem
	73 19 vstem
	-134 85 hstem
	return
	} NP
dup 165 {
	-134 23 hstem
	172 29 vstem
	73 19 vstem
	-244 23 hstem
	return
	} NP
dup 166 {
	73 128 vstem
	-73 23 hstem
	return
	} NP
dup 167 {
	73 128 vstem
	-68 18 hstem
	return
	} NP
dup 168 {
	101 24 vstem
	-244 20 hstem
	-73 23 hstem
	return
	} NP
dup 169 {
	73 128 vstem
	-73 23 hstem
	return
	} NP
dup 170 {
	110 53 vstem
	81 24 vstem
	-68 19 hstem
	167 24 vstem
	return
	} NP
dup 171 {
	175 24 vstem
	-244 19 hstem
	73 24 vstem
	-147 19 hstem
	return
	} NP
dup 172 {
	81 24 vstem
	-147 19 hstem
	167 24 vstem
	-68 19 hstem
	return
	} NP
dup 173 {
	-245 86 hstem
	return
	} NP
dup 174 {
	167 35 vstem
	-174 125 hstem
	return
	} NP
dup 175 {
	-182 23 hstem
	73 23 vstem
	-72 23 hstem
	177 25 vstem
	return
	} NP
ND
2 index /CharStrings 59 dict dup begin
/.notdef { 
0 391 hsbw
0 16 hstem
609 16 hstem
49 9 vstem
333 9 vstem
49 hmoveto
293 hlineto
625 vlineto
-293 hlineto
-625 vlineto
closepath
9 16 rmoveto
593 vlineto
275 hlineto
-593 vlineto
-275 hlineto
closepath
endchar } ND
/.null { 
0 0 hsbw
endchar } ND
/nonmarkingreturn { 
0 220 hsbw
endchar } ND
/space { 
0 220 hsbw
endchar } ND
/asterisk { 
0 122 hsbw
-122 20 hstem
859 20 hstem
24 25 vstem
73 25 vstem
49 -122 rmoveto
1001 vlineto
-25 hlineto
-1001 vlineto
25 hlineto
closepath
49 hmoveto
1001 vlineto
-25 hlineto
-1001 vlineto
25 hlineto
closepath
endchar } ND
/plus { 
0 244 hsbw
-122 20 hstem
859 20 hstem
0 24 vstem
49 24 vstem
24 -122 rmoveto
1001 vlineto
-24 hlineto
-1001 vlineto
24 hlineto
closepath
49 hmoveto
1001 vlineto
-24 hlineto
-1001 vlineto
24 hlineto
closepath
endchar } ND
/zero { 
0 342 hsbw
-244 24 hstem
-122 20 hstem
-72 23 hstem
859 20 hstem
73 23 vstem
167 23 vstem
269 24 vstem
317 25 vstem
190 -146 rmoveto
5 4 callsubr
56 -25 41 -33 vhcurveto
-34 -25 -41 -56 hvcurveto
-57 25 -41 34 vhcurveto
33 25 41 57 hvcurveto
closepath
-23 hmoveto
-41 -13 -33 -22 vhcurveto
-21 -15 30 44 hvcurveto
39 12 35 24 vhcurveto
20 15 -31 -43 hvcurveto
closepath
126 24 rmoveto
6 4 callsubr
1001 vlineto
-24 hlineto
-1001 vlineto
24 hlineto
closepath
49 hmoveto
1001 vlineto
-25 hlineto
-1001 vlineto
25 hlineto
closepath
endchar } ND
/one { 
0 342 hsbw
-244 20 hstem
-122 -1 hstem
-121 23 hstem
-69 20 hstem
859 20 hstem
147 24 vstem
269 24 vstem
317 25 vstem
171 -244 rmoveto
10 4 callsubr
195 vlineto
9 4 callsubr
-16 hlineto
8 4 callsubr
-8 -16 -21 -20 -27 -13 rrcurveto
-23 vlineto
7 4 callsubr
15 6 21 12 12 11 rrcurveto
-152 vlineto
10 4 callsubr
24 hlineto
closepath
122 122 rmoveto
11 4 callsubr
1001 vlineto
-24 hlineto
-1001 vlineto
24 hlineto
closepath
49 hmoveto
1001 vlineto
-25 hlineto
-1001 vlineto
25 hlineto
closepath
endchar } ND
/two { 
0 342 hsbw
-244 24 hstem
-122 20 hstem
-73 24 hstem
859 20 hstem
170 25 vstem
269 24 vstem
317 25 vstem
182 -244 rmoveto
7 4 4 8 hvcurveto
14 4 callsubr
8 -4 4 -7 vhcurveto
-76 hlineto
9 35 1 1 49 40 rrcurveto
16 14 14 14 0 21 rrcurveto
13 4 callsubr
28 -20 18 -32 vhcurveto
-30 -27 -22 -21 hvcurveto
-6 6 -5 6 vhcurveto
5 0 3 2 2 5 rrcurveto
12 4 callsubr
7 17 9 6 21 0 rrcurveto
15 10 -10 -14 hvcurveto
0 -11 -5 -8 -10 -7 rrcurveto
-53 -29 -9 -25 -17 -67 rrcurveto
106 hlineto
closepath
111 122 rmoveto
14 4 callsubr
1001 vlineto
-24 hlineto
-1001 vlineto
24 hlineto
closepath
49 hmoveto
1001 vlineto
-25 hlineto
-1001 vlineto
25 hlineto
closepath
endchar } ND
/three { 
0 342 hsbw
-244 25 hstem
-122 20 hstem
-72 23 hstem
859 20 hstem
160 23 vstem
269 24 vstem
317 25 vstem
183 -173 rmoveto
0 38 -17 23 -35 7 rrcurveto
34 36 rlineto
16 4 callsubr
7 7 3 5 0 2 rrcurveto
4 -2 2 -6 vhcurveto
-85 hlineto
-8 -4 -4 -8 hvcurveto
15 4 callsubr
-7 4 -4 8 vhcurveto
48 hlineto
-32 -35 rlineto
-4 -4 -1 -3 0 -4 rrcurveto
-4 4 -5 7 vhcurveto
41 -3 15 -11 0 -32 rrcurveto
-27 -16 -19 -24 vhcurveto
-17 0 -13 11 -6 18 rrcurveto
-2 6 -3 2 -5 0 rrcurveto
-7 -5 -4 -7 hvcurveto
3 -27 28 -24 28 0 rrcurveto
35 27 30 41 hvcurveto
closepath
110 51 rmoveto
17 4 callsubr
1001 vlineto
-24 hlineto
-1001 vlineto
24 hlineto
closepath
49 hmoveto
1001 vlineto
-25 hlineto
-1001 vlineto
25 hlineto
closepath
endchar } ND
/four { 
0 342 hsbw
-244 99 hstem
-122 20 hstem
-69 20 hstem
859 20 hstem
65 27 vstem
153 23 vstem
269 24 vstem
317 25 vstem
192 -213 rmoveto
22 4 callsubr
8 4 4 7 hvcurveto
21 4 callsubr
8 -4 4 -8 vhcurveto
-16 hlineto
33 vlineto
19 4 callsubr
8 -4 4 -7 vhcurveto
21 4 callsubr
-8 -4 -4 -8 hvcurveto
-33 vlineto
20 4 callsubr
-61 hlineto
43 129 rlineto
7 -6 5 -6 vhcurveto
-6 0 -3 -3 -2 -5 rrcurveto
-47 -133 rlineto
-23 1 0 6 vhcurveto
81 hlineto
19 4 callsubr
-19 vlineto
-8 4 -4 8 vhcurveto
18 4 callsubr
8 4 4 8 hvcurveto
19 vlineto
15 hlineto
closepath
101 91 rmoveto
22 4 callsubr
1001 vlineto
-24 hlineto
-1001 vlineto
24 hlineto
closepath
49 hmoveto
1001 vlineto
-25 hlineto
-1001 vlineto
25 hlineto
closepath
endchar } ND
/five { 
0 342 hsbw
-244 24 hstem
-122 20 hstem
-72 23 hstem
859 20 hstem
77 23 vstem
169 23 vstem
269 24 vstem
317 25 vstem
192 -175 rmoveto
25 4 callsubr
36 -29 32 -32 vhcurveto
-12 0 -10 -2 -9 -4 rrcurveto
41 vlineto
26 4 callsubr
66 hlineto
8 4 4 8 hvcurveto
25 4 callsubr
7 -4 4 -8 vhcurveto
-89 hlineto
-88 vlineto
24 4 callsubr
-7 3 -3 6 vhcurveto
25 4 callsubr
31 15 1 1 13 0 rrcurveto
21 17 -20 -24 hvcurveto
24 4 callsubr
-28 -14 -17 -22 vhcurveto
-17 0 -12 10 -8 22 rrcurveto
-2 5 -4 2 -5 0 rrcurveto
-6 -6 -4 -7 hvcurveto
23 4 callsubr
0 -21 24 -28 39 -3 rrcurveto
36 3 20 25 0 41 rrcurveto
closepath
101 53 rmoveto
27 4 callsubr
1001 vlineto
-24 hlineto
-1001 vlineto
24 hlineto
closepath
49 hmoveto
1001 vlineto
-25 hlineto
-1001 vlineto
25 hlineto
closepath
endchar } ND
/six { 
0 342 hsbw
-244 23 hstem
-122 20 hstem
-69 20 hstem
859 20 hstem
73 19 vstem
172 29 vstem
269 24 vstem
317 25 vstem
201 -173 rmoveto
29 4 callsubr
39 -23 23 -39 vhcurveto
-12 0 -10 -3 -10 -5 rrcurveto
14 20 12 14 23 14 rrcurveto
3 2 2 3 0 4 rrcurveto
28 4 callsubr
7 -5 6 -6 vhcurveto
-32 -45 -69 -49 hvcurveto
-47 27 -30 41 vhcurveto
35 25 29 42 hvcurveto
closepath
-29 hmoveto
29 4 callsubr
-26 -14 -22 -23 vhcurveto
-24 -19 20 23 hvcurveto
24 19 20 22 vhcurveto
22 17 -18 -21 hvcurveto
closepath
121 51 rmoveto
30 4 callsubr
1001 vlineto
-24 hlineto
-1001 vlineto
24 hlineto
closepath
49 hmoveto
1001 vlineto
-25 hlineto
-1001 vlineto
25 hlineto
closepath
endchar } ND
/seven { 
0 342 hsbw
-244 20 hstem
-122 20 hstem
-73 23 hstem
859 20 hstem
73 128 vstem
269 24 vstem
317 25 vstem
73 -73 rmoveto
33 4 callsubr
97 hlineto
-39 -44 -30 -74 0 -53 rrcurveto
24 hlineto
32 4 callsubr
6 65 29 67 41 44 rrcurveto
31 4 callsubr
18 vlineto
-128 hlineto
34 4 callsubr
-23 vlineto
closepath
220 -49 rmoveto
35 4 callsubr
1001 vlineto
-24 hlineto
-1001 vlineto
24 hlineto
closepath
49 hmoveto
1001 vlineto
-25 hlineto
-1001 vlineto
25 hlineto
closepath
endchar } ND
/eight { 
0 342 hsbw
-244 19 hstem
-147 19 hstem
-122 20 hstem
-68 19 hstem
859 20 hstem
73 24 vstem
110 53 vstem
167 24 vstem
269 24 vstem
317 25 vstem
110 -137 rmoveto
37 4 callsubr
-24 -6 -13 -17 0 -26 rrcurveto
-33 25 -25 38 vhcurveto
38 25 25 33 hvcurveto
36 4 callsubr
0 23 -12 18 -24 8 rrcurveto
19 7 9 14 0 17 rrcurveto
28 -22 22 -33 vhcurveto
-33 -22 -21 -28 hvcurveto
0 -18 9 -14 20 -7 rrcurveto
closepath
-5 40 rmoveto
38 4 callsubr
16 13 13 18 vhcurveto
18 13 -13 -18 hvcurveto
-16 -13 -13 -18 vhcurveto
-18 -13 13 18 hvcurveto
closepath
-8 -89 rmoveto
37 4 callsubr
23 17 16 22 vhcurveto
23 16 -17 -22 hvcurveto
-23 -16 -16 -22 vhcurveto
-23 -17 17 22 hvcurveto
closepath
196 64 rmoveto
1001 vlineto
-24 hlineto
-1001 vlineto
24 hlineto
closepath
49 hmoveto
1001 vlineto
-25 hlineto
-1001 vlineto
25 hlineto
closepath
endchar } ND
/nine { 
0 342 hsbw
-245 86 hstem
-122 20 hstem
-72 23 hstem
859 20 hstem
73 23 vstem
177 25 vstem
269 24 vstem
317 25 vstem
125 -245 rmoveto
41 4 callsubr
25 52 62 57 hvcurveto
48 -26 29 -42 vhcurveto
-36 -25 -29 -42 hvcurveto
-39 23 -23 39 vhcurveto
40 4 callsubr
13 0 10 3 9 5 rrcurveto
-15 -23 -12 -12 -21 -13 rrcurveto
-4 -2 -2 -4 0 -4 rrcurveto
39 4 callsubr
-6 5 -7 7 vhcurveto
closepath
52 130 rmoveto
41 4 callsubr
-24 -19 -20 -23 vhcurveto
-21 -18 18 21 hvcurveto
28 16 20 22 vhcurveto
23 20 -20 -23 hvcurveto
closepath
116 -7 rmoveto
42 4 callsubr
1001 vlineto
-24 hlineto
-1001 vlineto
24 hlineto
closepath
49 hmoveto
1001 vlineto
-25 hlineto
-1001 vlineto
25 hlineto
closepath
endchar } ND
/colon { 
0 73 hsbw
-122 20 hstem
859 20 hstem
0 24 vstem
49 24 vstem
24 -122 rmoveto
1001 vlineto
-24 hlineto
-1001 vlineto
24 hlineto
closepath
49 hmoveto
1001 vlineto
-24 hlineto
-1001 vlineto
24 hlineto
closepath
endchar } ND
/A { 
0 171 hsbw
-244 24 hstem
-72 23 hstem
0 20 hstem
859 20 hstem
30 23 vstem
73 49 vstem
124 22 vstem
122 hmoveto
43 4 callsubr
879 vlineto
-49 hlineto
-879 vlineto
49 hlineto
closepath
49 hmoveto
879 vlineto
-25 hlineto
-879 vlineto
25 hlineto
closepath
-25 -146 rmoveto
44 4 callsubr
56 -24 41 -34 vhcurveto
-33 -25 -41 -56 hvcurveto
-57 25 -41 33 vhcurveto
33 25 41 57 hvcurveto
closepath
-22 hmoveto
-41 -14 -33 -22 vhcurveto
-21 -14 30 44 hvcurveto
39 12 35 23 vhcurveto
21 15 -31 -43 hvcurveto
closepath
endchar } ND
/B { 
0 171 hsbw
-244 20 hstem
-121 23 hstem
-69 20 hstem
0 20 hstem
859 20 hstem
49 49 vstem
100 24 vstem
146 25 vstem
98 hmoveto
45 4 callsubr
879 vlineto
-49 hlineto
-879 vlineto
49 hlineto
closepath
73 hmoveto
879 vlineto
-25 hlineto
-879 vlineto
25 hlineto
closepath
-47 -244 rmoveto
48 4 callsubr
195 vlineto
47 4 callsubr
-16 hlineto
46 4 callsubr
-8 -16 -22 -20 -26 -13 rrcurveto
-23 vlineto
45 4 callsubr
15 6 21 12 12 11 rrcurveto
-152 vlineto
48 4 callsubr
24 hlineto
closepath
endchar } ND
/C { 
0 171 hsbw
-244 24 hstem
-73 24 hstem
0 20 hstem
859 20 hstem
49 24 vstem
122 24 vstem
171 hmoveto
49 4 callsubr
879 vlineto
-49 hlineto
-879 vlineto
49 hlineto
closepath
-98 hmoveto
879 vlineto
-24 hlineto
-879 vlineto
24 hlineto
closepath
60 -244 rmoveto
8 4 4 8 hvcurveto
50 4 callsubr
8 -4 4 -8 vhcurveto
-75 hlineto
9 35 0 1 49 40 rrcurveto
17 14 13 14 0 21 rrcurveto
51 4 callsubr
28 -20 18 -32 vhcurveto
-29 -27 -22 -21 hvcurveto
-6 5 -5 6 vhcurveto
5 0 3 2 2 5 rrcurveto
50 4 callsubr
7 17 9 6 21 0 rrcurveto
15 11 -10 -14 hvcurveto
0 -11 -6 -8 -10 -7 rrcurveto
49 4 callsubr
-53 -29 -9 -25 -17 -67 rrcurveto
106 hlineto
closepath
endchar } ND
/D { 
0 171 hsbw
-244 25 hstem
-73 24 hstem
0 20 hstem
859 20 hstem
24 99 vstem
124 22 vstem
123 hmoveto
52 4 callsubr
879 vlineto
-99 hlineto
-879 vlineto
99 hlineto
closepath
48 hmoveto
879 vlineto
-25 hlineto
-879 vlineto
25 hlineto
closepath
-25 -173 rmoveto
0 38 -17 22 -34 7 rrcurveto
34 36 rlineto
54 4 callsubr
7 7 3 5 0 3 rrcurveto
4 -2 2 -6 vhcurveto
-86 hlineto
-7 -4 -4 -8 hvcurveto
53 4 callsubr
-8 4 -4 7 vhcurveto
49 hlineto
-32 -34 rlineto
-4 -4 -1 -4 0 -3 rrcurveto
-5 4 -4 7 vhcurveto
42 -4 14 -12 0 -30 rrcurveto
-28 -18 -18 -22 vhcurveto
-17 0 -13 11 -6 18 rrcurveto
-2 5 -3 3 -5 0 rrcurveto
-7 -5 -5 -7 hvcurveto
3 -27 28 -23 28 0 rrcurveto
36 25 29 42 hvcurveto
closepath
endchar } ND
/E { 
0 171 hsbw
-244 99 hstem
-69 20 hstem
0 20 hstem
859 20 hstem
24 25 vstem
106 23 vstem
171 hmoveto
55 4 callsubr
879 vlineto
-49 hlineto
-879 vlineto
49 hlineto
closepath
-122 hmoveto
879 vlineto
-25 hlineto
-879 vlineto
25 hlineto
closepath
96 -213 rmoveto
58 4 callsubr
8 4 4 8 hvcurveto
59 4 callsubr
8 -4 4 -8 vhcurveto
-16 hlineto
33 vlineto
57 4 callsubr
8 -4 3 -8 vhcurveto
59 4 callsubr
-8 -4 -3 -8 hvcurveto
-33 vlineto
58 4 callsubr
-61 hlineto
44 129 rlineto
6 -7 5 -6 vhcurveto
-5 0 -4 -2 -2 -6 rrcurveto
-46 -133 rlineto
56 4 callsubr
-23 0 0 6 vhcurveto
82 hlineto
57 4 callsubr
-18 vlineto
-9 4 -4 8 vhcurveto
56 4 callsubr
7 4 4 9 hvcurveto
18 vlineto
55 4 callsubr
16 hlineto
closepath
endchar } ND
/F { 
0 171 hsbw
-244 24 hstem
-131 24 hstem
-72 23 hstem
0 20 hstem
859 20 hstem
32 23 vstem
124 22 vstem
73 hmoveto
60 4 callsubr
879 vlineto
-49 hlineto
-879 vlineto
49 hlineto
closepath
98 hmoveto
879 vlineto
-25 hlineto
-879 vlineto
25 hlineto
closepath
-25 -175 rmoveto
61 4 callsubr
36 -29 32 -31 vhcurveto
-12 0 -11 -2 -8 -4 rrcurveto
41 vlineto
63 4 callsubr
66 hlineto
7 4 4 8 hvcurveto
61 4 callsubr
7 -4 4 -7 vhcurveto
-89 hlineto
-88 vlineto
62 4 callsubr
-7 3 -3 6 vhcurveto
61 4 callsubr
30 15 1 1 14 0 rrcurveto
20 18 -20 -24 hvcurveto
62 4 callsubr
-28 -15 -17 -22 vhcurveto
-17 0 -12 10 -8 22 rrcurveto
-2 5 -3 2 -5 0 rrcurveto
-6 -6 -4 -7 hvcurveto
61 4 callsubr
0 -21 24 -28 39 -3 rrcurveto
35 3 20 25 0 41 rrcurveto
closepath
endchar } ND
/G { 
0 171 hsbw
-243 23 hstem
-133 23 hstem
0 20 hstem
859 20 hstem
21 25 vstem
126 23 vstem
49 hmoveto
64 4 callsubr
879 vlineto
-25 hlineto
-879 vlineto
25 hlineto
closepath
122 hmoveto
879 vlineto
-98 hlineto
-879 vlineto
98 hlineto
closepath
-22 -172 rmoveto
66 4 callsubr
39 -23 23 -39 vhcurveto
-12 0 -10 -3 -9 -5 rrcurveto
14 20 11 14 23 14 rrcurveto
4 2 1 3 0 4 rrcurveto
65 4 callsubr
7 -4 6 -7 vhcurveto
-31 -46 -69 -49 hvcurveto
-47 27 -30 41 vhcurveto
36 24 29 42 hvcurveto
closepath
-23 hmoveto
66 4 callsubr
-26 -14 -22 -23 vhcurveto
-24 -19 20 23 hvcurveto
24 19 20 22 vhcurveto
22 17 -18 -21 hvcurveto
closepath
endchar } ND
/H { 
0 171 hsbw
-243 20 hstem
-72 23 hstem
0 20 hstem
859 20 hstem
24 74 vstem
122 49 vstem
171 hmoveto
67 4 callsubr
879 vlineto
-49 hlineto
-879 vlineto
49 hlineto
closepath
-73 hmoveto
879 vlineto
-74 hlineto
-879 vlineto
74 hlineto
closepath
-76 -72 rmoveto
70 4 callsubr
97 hlineto
-40 -44 -29 -74 0 -53 rrcurveto
24 hlineto
69 4 callsubr
6 65 29 66 40 45 rrcurveto
68 4 callsubr
18 vlineto
-127 hlineto
-23 vlineto
closepath
endchar } ND
/I { 
0 171 hsbw
-244 19 hstem
-147 19 hstem
-68 19 hstem
0 20 hstem
859 20 hstem
21 23 57 53 123 23 vstem3
73 hmoveto
71 4 callsubr
879 vlineto
-49 hlineto
-879 vlineto
49 hlineto
closepath
98 hmoveto
879 vlineto
-73 hlineto
-879 vlineto
73 hlineto
closepath
-114 -137 rmoveto
73 4 callsubr
-23 -6 -13 -17 0 -26 rrcurveto
-33 25 -25 37 vhcurveto
38 25 25 33 hvcurveto
72 4 callsubr
0 23 -12 18 -24 8 rrcurveto
19 7 10 14 0 17 rrcurveto
28 -23 22 -33 vhcurveto
-33 -22 -21 -28 hvcurveto
0 -18 10 -14 19 -7 rrcurveto
closepath
-5 40 rmoveto
16 13 13 18 vhcurveto
18 14 -13 -18 hvcurveto
-16 -13 -13 -19 vhcurveto
-18 -13 13 18 hvcurveto
closepath
-8 -89 rmoveto
73 4 callsubr
23 17 16 22 vhcurveto
23 17 -17 -22 hvcurveto
-23 -17 -16 -22 vhcurveto
-23 -17 17 22 hvcurveto
closepath
endchar } ND
/J { 
0 171 hsbw
-244 86 hstem
-72 24 hstem
0 20 hstem
859 20 hstem
24 23 vstem
73 25 vstem
128 25 vstem
171 hmoveto
74 4 callsubr
879 vlineto
-49 hlineto
-879 vlineto
49 hlineto
closepath
-73 hmoveto
879 vlineto
-25 hlineto
-879 vlineto
25 hlineto
closepath
-22 -244 rmoveto
76 4 callsubr
25 52 62 57 hvcurveto
47 -26 30 -42 vhcurveto
-36 -25 -29 -42 hvcurveto
-40 23 -23 39 vhcurveto
75 4 callsubr
13 0 11 3 9 6 rrcurveto
-16 -24 -12 -12 -21 -13 rrcurveto
-4 -2 -2 -3 0 -4 rrcurveto
74 4 callsubr
-7 5 -6 7 vhcurveto
closepath
52 129 rmoveto
76 4 callsubr
-24 -19 -19 -23 vhcurveto
-21 -18 17 22 hvcurveto
27 16 20 22 vhcurveto
23 20 -19 -24 hvcurveto
closepath
endchar } ND
/K { 
0 171 hsbw
-244 24 hstem
-72 23 hstem
0 20 hstem
859 20 hstem
30 23 vstem
124 22 vstem
171 hmoveto
77 4 callsubr
879 vlineto
-73 hlineto
-879 vlineto
73 hlineto
closepath
-121 hmoveto
879 vlineto
-24 hlineto
-879 vlineto
24 hlineto
closepath
96 -146 rmoveto
78 4 callsubr
56 -24 41 -34 vhcurveto
-33 -25 -41 -56 hvcurveto
-57 25 -41 33 vhcurveto
33 25 41 57 hvcurveto
closepath
-22 hmoveto
-41 -14 -33 -22 vhcurveto
-21 -14 30 44 hvcurveto
39 12 35 23 vhcurveto
21 15 -31 -43 hvcurveto
closepath
endchar } ND
/L { 
0 171 hsbw
-244 20 hstem
-121 23 hstem
-69 20 hstem
0 20 hstem
859 20 hstem
24 49 vstem
100 24 vstem
73 hmoveto
79 4 callsubr
879 vlineto
-49 hlineto
-879 vlineto
49 hlineto
closepath
51 -244 rmoveto
82 4 callsubr
195 vlineto
81 4 callsubr
-16 hlineto
80 4 callsubr
-8 -16 -22 -20 -26 -13 rrcurveto
-23 vlineto
79 4 callsubr
15 6 21 12 12 11 rrcurveto
-152 vlineto
82 4 callsubr
24 hlineto
closepath
47 244 rmoveto
80 4 callsubr
879 vlineto
-49 hlineto
-879 vlineto
49 hlineto
closepath
endchar } ND
/M { 
0 171 hsbw
-244 24 hstem
-73 24 hstem
0 20 hstem
859 20 hstem
49 49 vstem
122 24 vstem
171 hmoveto
83 4 callsubr
879 vlineto
-49 hlineto
-879 vlineto
49 hlineto
closepath
-38 -244 rmoveto
8 4 4 8 hvcurveto
84 4 callsubr
8 -4 4 -8 vhcurveto
-75 hlineto
9 35 0 1 49 40 rrcurveto
17 14 13 14 0 21 rrcurveto
85 4 callsubr
28 -20 18 -32 vhcurveto
-29 -27 -22 -21 hvcurveto
-6 5 -5 6 vhcurveto
5 0 3 2 2 5 rrcurveto
84 4 callsubr
7 17 9 6 21 0 rrcurveto
15 11 -10 -14 hvcurveto
0 -11 -6 -8 -10 -7 rrcurveto
83 4 callsubr
-53 -29 -9 -25 -17 -67 rrcurveto
106 hlineto
closepath
-35 244 rmoveto
84 4 callsubr
879 vlineto
-49 hlineto
-879 vlineto
49 hlineto
closepath
endchar } ND
/N { 
0 171 hsbw
-244 25 hstem
-73 24 hstem
0 20 hstem
859 20 hstem
24 25 vstem
124 22 vstem
49 hmoveto
86 4 callsubr
879 vlineto
-25 hlineto
-879 vlineto
25 hlineto
closepath
97 -173 rmoveto
0 38 -17 22 -34 7 rrcurveto
34 36 rlineto
87 4 callsubr
7 7 3 5 0 3 rrcurveto
4 -2 2 -6 vhcurveto
-86 hlineto
-7 -4 -4 -8 hvcurveto
86 4 callsubr
-8 4 -4 7 vhcurveto
49 hlineto
-32 -34 rlineto
-4 -4 -1 -4 0 -3 rrcurveto
-5 4 -4 7 vhcurveto
42 -4 14 -12 0 -30 rrcurveto
-28 -18 -18 -22 vhcurveto
-17 0 -13 11 -6 18 rrcurveto
-2 5 -3 3 -5 0 rrcurveto
-7 -5 -5 -7 hvcurveto
3 -27 28 -23 28 0 rrcurveto
36 25 29 42 hvcurveto
closepath
25 173 rmoveto
87 4 callsubr
879 vlineto
-25 hlineto
-879 vlineto
25 hlineto
closepath
endchar } ND
/O { 
0 171 hsbw
-244 99 hstem
-69 20 hstem
0 20 hstem
859 20 hstem
106 23 vstem
146 25 vstem
171 hmoveto
88 4 callsubr
879 vlineto
-25 hlineto
-879 vlineto
25 hlineto
closepath
-47 hmoveto
879 vlineto
-75 hlineto
-879 vlineto
75 hlineto
closepath
21 -213 rmoveto
91 4 callsubr
8 4 4 8 hvcurveto
92 4 callsubr
8 -4 4 -8 vhcurveto
-16 hlineto
33 vlineto
90 4 callsubr
8 -4 3 -8 vhcurveto
92 4 callsubr
-8 -4 -3 -8 hvcurveto
-33 vlineto
91 4 callsubr
-61 hlineto
44 129 rlineto
6 -7 5 -6 vhcurveto
-5 0 -4 -2 -2 -6 rrcurveto
-46 -133 rlineto
89 4 callsubr
-23 0 0 6 vhcurveto
82 hlineto
90 4 callsubr
-18 vlineto
-9 4 -4 8 vhcurveto
89 4 callsubr
7 4 4 9 hvcurveto
18 vlineto
88 4 callsubr
16 hlineto
closepath
endchar } ND
/P { 
0 171 hsbw
-244 24 hstem
-131 24 hstem
-72 23 hstem
0 20 hstem
859 20 hstem
32 23 vstem
124 22 vstem
98 hmoveto
93 4 callsubr
879 vlineto
-74 hlineto
-879 vlineto
74 hlineto
closepath
73 hmoveto
879 vlineto
-25 hlineto
-879 vlineto
25 hlineto
closepath
-25 -175 rmoveto
94 4 callsubr
36 -29 32 -31 vhcurveto
-12 0 -11 -2 -8 -4 rrcurveto
41 vlineto
96 4 callsubr
66 hlineto
7 4 4 8 hvcurveto
94 4 callsubr
7 -4 4 -7 vhcurveto
-89 hlineto
-88 vlineto
95 4 callsubr
-7 3 -3 6 vhcurveto
94 4 callsubr
30 15 1 1 14 0 rrcurveto
20 18 -20 -24 hvcurveto
95 4 callsubr
-28 -15 -17 -22 vhcurveto
-17 0 -12 10 -8 22 rrcurveto
-2 5 -3 2 -5 0 rrcurveto
-6 -6 -4 -7 hvcurveto
94 4 callsubr
0 -21 24 -28 39 -3 rrcurveto
35 3 20 25 0 41 rrcurveto
closepath
endchar } ND
/Q { 
0 171 hsbw
-243 23 hstem
-133 23 hstem
0 20 hstem
859 20 hstem
21 25 vstem
98 24 vstem
126 23 vstem
122 hmoveto
97 4 callsubr
879 vlineto
-24 hlineto
-879 vlineto
24 hlineto
closepath
27 -172 rmoveto
98 4 callsubr
39 -23 23 -39 vhcurveto
-12 0 -10 -3 -9 -5 rrcurveto
14 20 11 14 23 14 rrcurveto
4 2 1 3 0 4 rrcurveto
97 4 callsubr
7 -4 6 -7 vhcurveto
-31 -46 -69 -49 hvcurveto
-47 27 -30 41 vhcurveto
36 24 29 42 hvcurveto
closepath
-23 hmoveto
98 4 callsubr
-26 -14 -22 -23 vhcurveto
-24 -19 20 23 hvcurveto
24 19 20 22 vhcurveto
22 17 -18 -21 hvcurveto
closepath
45 172 rmoveto
99 4 callsubr
879 vlineto
-25 hlineto
-879 vlineto
25 hlineto
closepath
endchar } ND
/R { 
0 171 hsbw
-243 20 hstem
-72 23 hstem
0 20 hstem
859 20 hstem
49 24 vstem
146 25 vstem
171 hmoveto
100 4 callsubr
879 vlineto
-25 hlineto
-879 vlineto
25 hlineto
closepath
-149 -72 rmoveto
103 4 callsubr
97 hlineto
-40 -44 -29 -74 0 -53 rrcurveto
24 hlineto
102 4 callsubr
6 65 29 66 40 45 rrcurveto
101 4 callsubr
18 vlineto
-127 hlineto
-23 vlineto
closepath
51 72 rmoveto
104 4 callsubr
879 vlineto
-24 hlineto
-879 vlineto
24 hlineto
closepath
endchar } ND
/S { 
0 171 hsbw
-244 19 hstem
-147 19 hstem
-68 19 hstem
0 20 hstem
859 20 hstem
21 23 vstem
73 25 vstem
123 23 vstem
171 hmoveto
105 4 callsubr
879 vlineto
-25 hlineto
-879 vlineto
25 hlineto
closepath
-114 -137 rmoveto
106 4 callsubr
-23 -6 -13 -17 0 -26 rrcurveto
-33 25 -25 37 vhcurveto
38 25 25 33 hvcurveto
105 4 callsubr
0 23 -12 18 -24 8 rrcurveto
19 7 10 14 0 17 rrcurveto
28 -23 22 -33 vhcurveto
-33 -22 -21 -28 hvcurveto
0 -18 10 -14 19 -7 rrcurveto
closepath
-5 40 rmoveto
107 4 callsubr
16 13 13 18 vhcurveto
18 14 -13 -18 hvcurveto
-16 -13 -13 -19 vhcurveto
-18 -13 13 18 hvcurveto
closepath
-8 -89 rmoveto
106 4 callsubr
23 17 16 22 vhcurveto
23 17 -17 -22 hvcurveto
-23 -17 -16 -22 vhcurveto
-23 -17 17 22 hvcurveto
closepath
54 186 rmoveto
879 vlineto
-25 hlineto
-879 vlineto
25 hlineto
closepath
endchar } ND
/T { 
0 171 hsbw
-244 86 hstem
-72 24 hstem
0 20 hstem
859 20 hstem
24 23 vstem
49 24 vstem
128 25 vstem
170 hmoveto
108 4 callsubr
879 vlineto
-72 hlineto
-879 vlineto
72 hlineto
closepath
-97 hmoveto
879 vlineto
-24 hlineto
-879 vlineto
24 hlineto
closepath
3 -244 rmoveto
110 4 callsubr
25 52 62 57 hvcurveto
47 -26 30 -42 vhcurveto
-36 -25 -29 -42 hvcurveto
-40 23 -23 39 vhcurveto
109 4 callsubr
13 0 11 3 9 6 rrcurveto
-16 -24 -12 -12 -21 -13 rrcurveto
-4 -2 -2 -3 0 -4 rrcurveto
108 4 callsubr
-7 5 -6 7 vhcurveto
closepath
52 129 rmoveto
110 4 callsubr
-24 -19 -19 -23 vhcurveto
-21 -18 17 22 hvcurveto
27 16 20 22 vhcurveto
23 20 -19 -24 hvcurveto
closepath
endchar } ND
/a { 
0 171 hsbw
-244 24 hstem
-72 23 hstem
0 20 hstem
859 20 hstem
30 23 vstem
124 22 vstem
73 hmoveto
111 4 callsubr
879 vlineto
-73 hlineto
-879 vlineto
73 hlineto
closepath
73 hmoveto
879 vlineto
-24 hlineto
-879 vlineto
24 hlineto
closepath
-146 vmoveto
112 4 callsubr
56 -24 41 -34 vhcurveto
-33 -25 -41 -56 hvcurveto
-57 25 -41 33 vhcurveto
33 25 41 57 hvcurveto
closepath
-22 hmoveto
-41 -14 -33 -22 vhcurveto
-21 -14 30 44 hvcurveto
39 12 35 23 vhcurveto
21 15 -31 -43 hvcurveto
closepath
endchar } ND
/b { 
0 171 hsbw
-244 20 hstem
-121 23 hstem
-69 20 hstem
0 20 hstem
859 20 hstem
0 49 vstem
100 24 vstem
49 hmoveto
113 4 callsubr
879 vlineto
-49 hlineto
-879 vlineto
49 hlineto
closepath
75 -244 rmoveto
116 4 callsubr
195 vlineto
115 4 callsubr
-16 hlineto
114 4 callsubr
-8 -16 -22 -20 -26 -13 rrcurveto
-23 vlineto
113 4 callsubr
15 6 21 12 12 11 rrcurveto
-152 vlineto
116 4 callsubr
24 hlineto
closepath
22 244 rmoveto
114 4 callsubr
879 vlineto
-48 hlineto
-879 vlineto
48 hlineto
closepath
endchar } ND
/c { 
0 171 hsbw
-244 24 hstem
-73 24 hstem
0 20 hstem
859 20 hstem
0 49 vstem
122 24 vstem
49 hmoveto
117 4 callsubr
879 vlineto
-49 hlineto
-879 vlineto
49 hlineto
closepath
84 -244 rmoveto
8 4 4 8 hvcurveto
119 4 callsubr
8 -4 4 -8 vhcurveto
-75 hlineto
9 35 0 1 49 40 rrcurveto
17 14 13 14 0 21 rrcurveto
118 4 callsubr
28 -20 18 -32 vhcurveto
-29 -27 -22 -21 hvcurveto
-6 5 -5 6 vhcurveto
5 0 3 2 2 5 rrcurveto
117 4 callsubr
7 17 9 6 21 0 rrcurveto
15 11 -10 -14 hvcurveto
0 -11 -6 -8 -10 -7 rrcurveto
-53 -29 -9 -25 -17 -67 rrcurveto
106 hlineto
closepath
-11 244 rmoveto
120 4 callsubr
879 vlineto
-49 hlineto
-879 vlineto
49 hlineto
closepath
endchar } ND
/d { 
0 171 hsbw
-244 25 hstem
-73 24 hstem
0 20 hstem
859 20 hstem
0 24 vstem
124 22 vstem
24 hmoveto
121 4 callsubr
879 vlineto
-24 hlineto
-879 vlineto
24 hlineto
closepath
122 -173 rmoveto
0 38 -17 22 -34 7 rrcurveto
34 36 rlineto
122 4 callsubr
7 7 3 5 0 3 rrcurveto
4 -2 2 -6 vhcurveto
-86 hlineto
-7 -4 -4 -8 hvcurveto
121 4 callsubr
-8 4 -4 7 vhcurveto
49 hlineto
-32 -34 rlineto
-4 -4 -1 -4 0 -3 rrcurveto
-5 4 -4 7 vhcurveto
42 -4 14 -12 0 -30 rrcurveto
-28 -18 -18 -22 vhcurveto
-17 0 -13 11 -6 18 rrcurveto
-2 5 -3 3 -5 0 rrcurveto
-7 -5 -5 -7 hvcurveto
3 -27 28 -23 28 0 rrcurveto
36 25 29 42 hvcurveto
closepath
173 vmoveto
123 4 callsubr
879 vlineto
-24 hlineto
-879 vlineto
24 hlineto
closepath
endchar } ND
/e { 
0 171 hsbw
-244 99 hstem
-69 20 hstem
0 20 hstem
859 20 hstem
0 24 vstem
106 23 vstem
24 hmoveto
124 4 callsubr
879 vlineto
-24 hlineto
-879 vlineto
24 hlineto
closepath
100 hmoveto
879 vlineto
-75 hlineto
-879 vlineto
75 hlineto
closepath
21 -213 rmoveto
127 4 callsubr
8 4 4 8 hvcurveto
128 4 callsubr
8 -4 4 -8 vhcurveto
-16 hlineto
33 vlineto
126 4 callsubr
8 -4 3 -8 vhcurveto
128 4 callsubr
-8 -4 -3 -8 hvcurveto
-33 vlineto
127 4 callsubr
-61 hlineto
44 129 rlineto
6 -7 5 -6 vhcurveto
-5 0 -4 -2 -2 -6 rrcurveto
-46 -133 rlineto
125 4 callsubr
-23 0 0 6 vhcurveto
82 hlineto
126 4 callsubr
-18 vlineto
-9 4 -4 8 vhcurveto
125 4 callsubr
7 4 4 9 hvcurveto
18 vlineto
124 4 callsubr
16 hlineto
closepath
endchar } ND
/f { 
0 171 hsbw
-244 24 hstem
-131 24 hstem
-72 23 hstem
0 20 hstem
859 20 hstem
0 24 vstem
32 23 vstem
124 22 vstem
146 hmoveto
129 4 callsubr
879 vlineto
-73 hlineto
-879 vlineto
73 hlineto
closepath
-122 hmoveto
879 vlineto
-24 hlineto
-879 vlineto
24 hlineto
closepath
122 -175 rmoveto
130 4 callsubr
36 -29 32 -31 vhcurveto
-12 0 -11 -2 -8 -4 rrcurveto
41 vlineto
132 4 callsubr
66 hlineto
7 4 4 8 hvcurveto
130 4 callsubr
7 -4 4 -7 vhcurveto
-89 hlineto
-88 vlineto
131 4 callsubr
-7 3 -3 6 vhcurveto
130 4 callsubr
30 15 1 1 14 0 rrcurveto
20 18 -20 -24 hvcurveto
131 4 callsubr
-28 -15 -17 -22 vhcurveto
-17 0 -12 10 -8 22 rrcurveto
-2 5 -3 2 -5 0 rrcurveto
-6 -6 -4 -7 hvcurveto
130 4 callsubr
0 -21 24 -28 39 -3 rrcurveto
35 3 20 25 0 41 rrcurveto
closepath
endchar } ND
/g { 
0 171 hsbw
-243 23 hstem
-133 23 hstem
0 20 hstem
859 20 hstem
0 24 vstem
49 24 vstem
126 23 vstem
24 hmoveto
133 4 callsubr
879 vlineto
-24 hlineto
-879 vlineto
24 hlineto
closepath
125 -172 rmoveto
135 4 callsubr
39 -23 23 -39 vhcurveto
-12 0 -10 -3 -9 -5 rrcurveto
14 20 11 14 23 14 rrcurveto
4 2 1 3 0 4 rrcurveto
134 4 callsubr
7 -4 6 -7 vhcurveto
-31 -46 -69 -49 hvcurveto
133 4 callsubr
-47 27 -30 41 vhcurveto
36 24 29 42 hvcurveto
closepath
-23 hmoveto
135 4 callsubr
-26 -14 -22 -23 vhcurveto
-24 -19 20 23 hvcurveto
24 19 20 22 vhcurveto
22 17 -18 -21 hvcurveto
closepath
-53 172 rmoveto
879 vlineto
-24 hlineto
-879 vlineto
24 hlineto
closepath
endchar } ND
/h { 
0 171 hsbw
-243 20 hstem
-72 23 hstem
0 20 hstem
859 20 hstem
0 24 50 22 98 24 vstem3
122 hmoveto
136 4 callsubr
879 vlineto
-24 hlineto
-879 vlineto
24 hlineto
closepath
-100 -72 rmoveto
139 4 callsubr
97 hlineto
-40 -44 -29 -74 0 -53 rrcurveto
24 hlineto
138 4 callsubr
6 65 29 66 40 45 rrcurveto
137 4 callsubr
18 vlineto
-127 hlineto
-23 vlineto
closepath
2 72 rmoveto
139 4 callsubr
879 vlineto
-24 hlineto
-879 vlineto
24 hlineto
closepath
endchar } ND
/i { 
0 171 hsbw
-244 19 hstem
-147 19 hstem
-68 19 hstem
0 20 hstem
859 20 hstem
21 23 vstem
73 25 vstem
123 23 vstem
98 hmoveto
140 4 callsubr
879 vlineto
-25 hlineto
-879 vlineto
25 hlineto
closepath
-41 -137 rmoveto
142 4 callsubr
-23 -6 -13 -17 0 -26 rrcurveto
-33 25 -25 37 vhcurveto
38 25 25 33 hvcurveto
141 4 callsubr
0 23 -12 18 -24 8 rrcurveto
19 7 10 14 0 17 rrcurveto
28 -23 22 -33 vhcurveto
-33 -22 -21 -28 hvcurveto
0 -18 10 -14 19 -7 rrcurveto
closepath
-5 40 rmoveto
16 13 13 18 vhcurveto
18 14 -13 -18 hvcurveto
-16 -13 -13 -19 vhcurveto
-18 -13 13 18 hvcurveto
closepath
-8 -89 rmoveto
142 4 callsubr
23 17 16 22 vhcurveto
23 17 -17 -22 hvcurveto
-23 -17 -16 -22 vhcurveto
-23 -17 17 22 hvcurveto
closepath
-20 186 rmoveto
141 4 callsubr
879 vlineto
-24 hlineto
-879 vlineto
24 hlineto
closepath
endchar } ND
/j { 
0 171 hsbw
-244 86 hstem
-72 24 hstem
0 20 hstem
859 20 hstem
24 23 vstem
98 24 vstem
128 25 vstem
73 hmoveto
143 4 callsubr
879 vlineto
-73 hlineto
-879 vlineto
73 hlineto
closepath
49 hmoveto
879 vlineto
-24 hlineto
-879 vlineto
24 hlineto
closepath
-46 -244 rmoveto
145 4 callsubr
25 52 62 57 hvcurveto
47 -26 30 -42 vhcurveto
-36 -25 -29 -42 hvcurveto
-40 23 -23 39 vhcurveto
144 4 callsubr
13 0 11 3 9 6 rrcurveto
-16 -24 -12 -12 -21 -13 rrcurveto
-4 -2 -2 -3 0 -4 rrcurveto
143 4 callsubr
-7 5 -6 7 vhcurveto
closepath
52 129 rmoveto
145 4 callsubr
-24 -19 -19 -23 vhcurveto
-21 -18 17 22 hvcurveto
27 16 20 22 vhcurveto
23 20 -19 -24 hvcurveto
closepath
endchar } ND
/k { 
0 269 hsbw
-244 24 hstem
-72 23 hstem
73 23 vstem
167 23 vstem
190 -146 rmoveto
56 -25 41 -33 vhcurveto
-34 -25 -41 -56 hvcurveto
-57 25 -41 34 vhcurveto
33 25 41 57 hvcurveto
closepath
-23 hmoveto
-41 -13 -33 -22 vhcurveto
-21 -15 30 44 hvcurveto
39 12 35 24 vhcurveto
20 15 -31 -43 hvcurveto
closepath
endchar } ND
/l { 
0 269 hsbw
-244 20 hstem
-121 23 hstem
-69 20 hstem
147 24 vstem
171 -244 rmoveto
149 4 callsubr
195 vlineto
148 4 callsubr
-16 hlineto
147 4 callsubr
-8 -16 -21 -20 -27 -13 rrcurveto
-23 vlineto
146 4 callsubr
15 6 21 12 12 11 rrcurveto
-152 vlineto
149 4 callsubr
24 hlineto
closepath
endchar } ND
/m { 
0 269 hsbw
-244 24 hstem
-73 24 hstem
170 25 vstem
182 -244 rmoveto
7 4 4 8 hvcurveto
152 4 callsubr
8 -4 4 -7 vhcurveto
-76 hlineto
9 35 1 1 49 40 rrcurveto
16 14 14 14 0 21 rrcurveto
151 4 callsubr
28 -20 18 -32 vhcurveto
-30 -27 -22 -21 hvcurveto
-6 6 -5 6 vhcurveto
5 0 3 2 2 5 rrcurveto
150 4 callsubr
7 17 9 6 21 0 rrcurveto
15 10 -10 -14 hvcurveto
0 -11 -5 -8 -10 -7 rrcurveto
-53 -29 -9 -25 -17 -67 rrcurveto
106 hlineto
closepath
endchar } ND
/n { 
0 269 hsbw
-244 25 hstem
-72 23 hstem
160 23 vstem
183 -173 rmoveto
0 38 -17 23 -35 7 rrcurveto
34 36 rlineto
154 4 callsubr
7 7 3 5 0 2 rrcurveto
4 -2 2 -6 vhcurveto
-85 hlineto
-8 -4 -4 -8 hvcurveto
153 4 callsubr
-7 4 -4 8 vhcurveto
48 hlineto
-32 -35 rlineto
-4 -4 -1 -3 0 -4 rrcurveto
-4 4 -5 7 vhcurveto
41 -3 15 -11 0 -32 rrcurveto
-27 -16 -19 -24 vhcurveto
-17 0 -13 11 -6 18 rrcurveto
-2 6 -3 2 -5 0 rrcurveto
-7 -5 -4 -7 hvcurveto
3 -27 28 -24 28 0 rrcurveto
35 27 30 41 hvcurveto
closepath
endchar } ND
/o { 
0 269 hsbw
-244 99 hstem
-69 20 hstem
65 27 vstem
153 23 vstem
192 -213 rmoveto
159 4 callsubr
8 4 4 7 hvcurveto
158 4 callsubr
8 -4 4 -8 vhcurveto
-16 hlineto
33 vlineto
156 4 callsubr
8 -4 4 -7 vhcurveto
158 4 callsubr
-8 -4 -4 -8 hvcurveto
-33 vlineto
157 4 callsubr
-61 hlineto
43 129 rlineto
7 -6 5 -6 vhcurveto
-6 0 -3 -3 -2 -5 rrcurveto
-47 -133 rlineto
-23 1 0 6 vhcurveto
81 hlineto
156 4 callsubr
-19 vlineto
-8 4 -4 8 vhcurveto
155 4 callsubr
8 4 4 8 hvcurveto
19 vlineto
15 hlineto
closepath
endchar } ND
/p { 
0 269 hsbw
-244 24 hstem
-131 24 hstem
-72 23 hstem
77 23 vstem
169 23 vstem
192 -175 rmoveto
162 4 callsubr
36 -29 32 -32 vhcurveto
-12 0 -10 -2 -9 -4 rrcurveto
41 vlineto
163 4 callsubr
66 hlineto
8 4 4 8 hvcurveto
162 4 callsubr
7 -4 4 -8 vhcurveto
-89 hlineto
-88 vlineto
161 4 callsubr
-7 3 -3 6 vhcurveto
162 4 callsubr
31 15 1 1 13 0 rrcurveto
21 17 -20 -24 hvcurveto
161 4 callsubr
-28 -14 -17 -22 vhcurveto
-17 0 -12 10 -8 22 rrcurveto
-2 5 -4 2 -5 0 rrcurveto
-6 -6 -4 -7 hvcurveto
160 4 callsubr
0 -21 24 -28 39 -3 rrcurveto
36 3 20 25 0 41 rrcurveto
closepath
endchar } ND
/q { 
0 269 hsbw
-244 23 hstem
-134 23 hstem
-69 20 hstem
73 19 vstem
172 29 vstem
201 -173 rmoveto
165 4 callsubr
39 -23 23 -39 vhcurveto
-12 0 -10 -3 -10 -5 rrcurveto
14 20 12 14 23 14 rrcurveto
3 2 2 3 0 4 rrcurveto
164 4 callsubr
7 -5 6 -6 vhcurveto
-32 -45 -69 -49 hvcurveto
-47 27 -30 41 vhcurveto
35 25 29 42 hvcurveto
closepath
-29 hmoveto
165 4 callsubr
-26 -14 -22 -23 vhcurveto
-24 -19 20 23 hvcurveto
24 19 20 22 vhcurveto
22 17 -18 -21 hvcurveto
closepath
endchar } ND
/r { 
0 269 hsbw
-244 20 hstem
-73 23 hstem
73 128 vstem
73 -73 rmoveto
168 4 callsubr
97 hlineto
-39 -44 -30 -74 0 -53 rrcurveto
24 hlineto
167 4 callsubr
6 65 29 67 41 44 rrcurveto
166 4 callsubr
18 vlineto
-128 hlineto
169 4 callsubr
-23 vlineto
closepath
endchar } ND
/s { 
0 269 hsbw
-244 19 hstem
-147 19 hstem
-68 19 hstem
73 24 vstem
110 53 vstem
167 24 vstem
110 -137 rmoveto
171 4 callsubr
-24 -6 -13 -17 0 -26 rrcurveto
-33 25 -25 38 vhcurveto
38 25 25 33 hvcurveto
170 4 callsubr
0 23 -12 18 -24 8 rrcurveto
19 7 9 14 0 17 rrcurveto
28 -22 22 -33 vhcurveto
-33 -22 -21 -28 hvcurveto
0 -18 9 -14 20 -7 rrcurveto
closepath
-5 40 rmoveto
172 4 callsubr
16 13 13 18 vhcurveto
18 13 -13 -18 hvcurveto
-16 -13 -13 -18 vhcurveto
-18 -13 13 18 hvcurveto
closepath
-8 -89 rmoveto
171 4 callsubr
23 17 16 22 vhcurveto
23 16 -17 -22 hvcurveto
-23 -16 -16 -22 vhcurveto
-23 -17 17 22 hvcurveto
closepath
endchar } ND
/t { 
0 269 hsbw
-245 86 hstem
-72 23 hstem
73 23 vstem
177 25 vstem
125 -245 rmoveto
175 4 callsubr
25 52 62 57 hvcurveto
48 -26 29 -42 vhcurveto
-36 -25 -29 -42 hvcurveto
-39 23 -23 39 vhcurveto
174 4 callsubr
13 0 10 3 9 5 rrcurveto
-15 -23 -12 -12 -21 -13 rrcurveto
-4 -2 -2 -4 0 -4 rrcurveto
173 4 callsubr
-6 5 -7 7 vhcurveto
closepath
52 130 rmoveto
175 4 callsubr
-24 -19 -20 -23 vhcurveto
-21 -18 18 21 hvcurveto
28 16 20 22 vhcurveto
23 20 -20 -23 hvcurveto
closepath
endchar } ND
/bracketleft { 
0 98 hsbw
0 20 hstem
859 20 hstem
0 24 vstem
49 49 vstem
98 hmoveto
879 vlineto
-49 hlineto
-879 vlineto
49 hlineto
closepath
-74 hmoveto
879 vlineto
-24 hlineto
-879 vlineto
24 hlineto
closepath
endchar } ND
/backslash { 
0 49 hsbw
0 20 hstem
859 20 hstem
24 25 vstem
49 hmoveto
879 vlineto
-25 hlineto
-879 vlineto
25 hlineto
closepath
endchar } ND
end
end
readonly put
noaccess put
dup/FontName get exch definefont pop
mark currentfile closefile
cleartomark
