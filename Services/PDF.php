<?php

namespace Alienor\PDFBundle\Services;

use setasign\Fpdi\Fpdi as FPDI;

/**
 * Un fichier PDF
 *
 * <AUTHOR> Passault <<EMAIL>>
 */
class PDF extends FPDI 
{
	/**
	 * Résolution voulue en pixels par mm
	 * 12px/mm = 300dpi
	 */
	public static $resolution = 12;

	/**
	 * Obtenir les dimensions de la première page d'un PDF
	 *
	 * @param $pdf le nom du fichier
	 */
	public function getPdfSize($file)
	{
		$pdf = new FPDI;
		$pdf->AddPage();
		$pdf->setSourceFile($file);
		$tplIdx = $pdf->importPage(1);
		return $pdf->getTemplateSize($tplIdx);
	}

	/**
	 * Importe la première page d'un PDF dans le fichier
	 *
	 * @param string le fichier pdf
	 */
	public function importPdf($file)
	{
		$size = $this->getPdfSize($file);

		$this->AddPage();
		$this->setSourceFile($file);
		$tplIdx = $this->importPage(1);
		$this->useTemplate($tplIdx);

		return $this;
	}

	/**
	 * Place une image dans une "boîte"
	 *
	 * @param string $file le fichier
	 * @param array $box la zone ou placer l'image (w, h, x, y)
	 */
	public function imageBox($file, $box, $type = null)
	{
		// Dimensions de l'image en mm
		$size = getimagesize($file);
		$w = $size[0] / self::$resolution;
		$h = $size[1] / self::$resolution;

		// Largeur et hauteur de l'emplacement
		$dW = $box['w'];
		$dH = $box['h'];

		// Calcul de l'échelle
		$scale = 1.0;

		if ($w > $dW)
			$scale = $w/$dW;
		if ($h > $dW && $h/$dH > $scale)
			$scale = $h/$dH;

		// Largeur et hauteur de l'image cible
		$imageW = ($w/$scale);
		$imageH = ($h/$scale);

		$xTop = ($dW - $imageW) / 2;
		$yTop = ($dH - $imageH) / 2;

		// Inclusion de l'image dans le document
		$this->image($file, $box['x'] + $xTop, $box['y'] + $yTop, $imageW, $imageH, $type);
	}

	/**
	 * Ecrit dans une "boîte"
	 *
	 * @param string la chaîne à écrire
	 * @param array $box la zone ou écrire
	 * @param string l'alignement
	 */
	public function writeBox($text, $box, $align = 'L') 
	{
		// Fix sur l'encodage du symbole euro
		$text = str_replace(utf8_decode('€'), chr(128), utf8_decode($text));

		$this->setXY($box['x'], $box['y']);
		$this->multiCell($box['w'], $box['h'], $text, 0, $align);
	}
}
