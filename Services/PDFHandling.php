<?php

namespace Alienor\PDFBundle\Services;

use Alienor\PDFBundle\Services\PDF;

/**
 * Gestionnaire de PDFs
 *
 * <AUTHOR> Passault <<EMAIL>>
 */
class PDFHandling
{
	/**
	 * Créer un fichier PDF vide
	 */
	public function createPdf($w = null, $h = null)
	{
		if (null != $w) {
			$orientation = ($w < $h) ? 'P' : 'L';
			return new PDF($orientation, 'mm', array($w, $h));
		} else {
			return new PDF();
		}
	}

	/**
	 * Créer un fichier PDF à partir d'un autre
	 */
	public function createPdfFromModel($file)
	{
		$f = new PDF();
		$size = $f->getPdfSize($file);
		$pdf = $this->createPdf($size['w'], $size['h']);
		$pdf->importPdf($file);
		return $pdf;
	}
}

