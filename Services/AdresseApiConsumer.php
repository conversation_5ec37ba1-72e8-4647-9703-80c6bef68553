<?php

namespace Alienor\RequeteurAdresseBundle\Services;

use Alienor\LoggerWebserviceBundle\Logger\WsLogger;

/*
* Documentation de l'API : http://geo.datalake.int.alienor.net/
*/
class AdresseApiConsumer {	
    
    protected $apiUrl;
    protected $apiUrlCommune;
    protected $logger;

    public function __construct($adresse_api_url, $adresse_api_url_commune, ?WsLogger $wsLogger = null)
    {
        $this->apiUrl = $adresse_api_url;
        $this->apiUrlCommune = $adresse_api_url_commune;
        $this->logger = $wsLogger;
    }

    public function search($research, $autocomplete = false)
    {
        $apiResponse = $this->call([
            'q' => $research,
            'autocomplete' => ($autocomplete ? '1' : '0')
        ]);
        if ($apiResponse) {
            return $apiResponse['features'];
        }
        return [];
    }

    public function getCityFromCodePost($postCode)
    {
        try {
            $url = $this->apiUrlCommune.'/communes?'.http_build_query(['codePostal' => $postCode]);
            $apiResponse = json_decode(file_get_contents($url), true);
        } catch (\Exception $e) {
            return [];
        }
        if ($apiResponse && isset($apiResponse)) {
            $apiResponse = array_map(function ($adresse) use ($postCode) {
                if (in_array($postCode, $adresse['codesPostaux'])) {
                    $adresse['postcode'] = $postCode;
                } else {
                    $adresse['postcode'] = $adresse['codesPostaux'][0];
                }
                $adresse['city'] = $adresse['nom'];
                return ['properties' => $adresse];
            }, $apiResponse);
            return $apiResponse;
        }
        return [];
    }

    protected function call($params) {
        $logStart = $this->logger->logQueryStart();
        try {
            $url = $this->apiUrl.'/search/?'.http_build_query($params);
            $apiResponse = json_decode(file_get_contents($url), true);
            $this->logger->logQueryStop($logStart, $url, $params, $http_response_header[0]);
            return $apiResponse;
        } catch (\Exception $e) {
            $this->logger->logQueryStop($logStart, $url, $params, $e->getMessage());
            return false;
        }
    }
}
