<?php

namespace Alienor\RequeteurAdresseBundle\Controller;

use Alienor\RequeteurAdresseBundle\Services\AdresseApiConsumer;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;

/**
 *
 */
class RequeteurAdresseController extends AbstractController
{
    /**
     * Recherche d'une ville
     * @Route("/requeteuradresse/js_ville", name="js_recherche_ville")
     * @return array
     */
    public function jsVilleAction(Request $request, AdresseApiConsumer $adresseApiConsumer) {
        $params = $request->query->all();
        if (!isset($params['codePostal'])) {
            throw new \Exception("Vous devez definir le champ query", 1);
        }
        $response = new JsonResponse();
        $response->setData($adresseApiConsumer->getCityFromCodePost($params['codePostal']));
        return $response;
    }

    /**
     * AutoCompletion Adresse
     * @Route("/requeteuradresse/js_completionAdresse", name="js_completion_adresse")
     * @return array
     */
    public function jsCompletionAction(Request $request, AdresseApiConsumer $adresseApiConsumer) {
        $params = $request->query->all();
        if (!isset($params['query'])) {
            throw new \Exception("Vous devez definir le champ query", 1);
        }
        // requete à l'API
        $items = $adresseApiConsumer->search($params['query'], true);
        $response = new JsonResponse();
        $response->setData($items);
        return $response;
    }
}