<?php

namespace Alienor\SymfonyProfilerExtraGit\DataCollector;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\DataCollector\DataCollector;
use Alienor\SymfonyProfilerExtraGit\BranchLoader\GitLoader;

class GitDataCollector extends DataCollector
{
    public function __construct(private readonly GitLoader $gitLoader)
    {
    }

    public function collect(Request $request, Response $response, ?\Throwable $exception = null): void
    {
       // We add the git informations in $data[]
        $this->data = [
            'git_branch' => $this->gitLoader->getBranchName(),
            'last_commit_message' => $this->gitLoader->getLastCommitMessage(),
            'logs' => $this->gitLoader->getLastCommitDetail(),
            'submodules' => $this->gitLoader->getSubmodulesDetail(),
        ];

        $this->data['statusColor'] = '';
        if ($this->isMasterBranch() || $this->isPreprodBranch()) {
            $this->data['statusColor'] = 'red';
        }
        if ($this->data['statusColor'] != 'red') {
            if (!$this->isFeatureBranch()) {
                $this->data['statusColor'] = 'yellow';
            }
            foreach ($this->data['submodules'] as $submodule) {
                if ($submodule === 'preprod') {
                    $this->data['statusColor'] = 'red';
                    break;
                } elseif ($submodule === 'develop') {
                    $this->data['statusColor'] = 'yellow';
                }
            }
        }
    }

    /**
     * @return string
     */
    public function getName()
    {
        return 'app.git_data_collector';
    }

    public function reset(): void
    {
        $this->data = array();
    }
    //Some helpers to access more easily to infos in the template
    public function getGitBranch()
    {
        return $this->data['git_branch'];
    }

    public function getLastCommitMessage()
    {
        return $this->data['last_commit_message'];
    }

    public function getLastCommitAuthor()
    {
        return $this->data['logs']['author'];
    }

    public function getLastCommitDate()
    {
        return $this->data['logs']['date'];
    }

    public function getSubmodules()
    {
        return $this->data['submodules'];
    }

    public function getStatusColor()
    {
        return $this->data['statusColor'];
    }

    public function isMasterBranch(): bool
    {
        return false !== $this->checkBranchName('master');
    }

    public function isPreprodBranch(): bool
    {
        return false !== $this->checkBranchName('preprod');
    }

    public function isFeatureBranch(): bool
    {
        return false !== $this->checkBranchName('feature') || false !== $this->checkBranchName('ft') || false !== $this->checkBranchName('feat');
    }

    /**
     * @param string $str
     * @return false|int
     */
    public function checkBranchName(string $str): int|false
    {
        return strpos($this->data['git_branch'], $str);
    }
}
