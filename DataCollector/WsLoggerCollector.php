<?php

namespace Alienor\LoggerWebserviceBundle\DataCollector;

use Alienor\LoggerWebserviceBundle\Logger\WsLogger;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpKernel\DataCollector\DataCollector;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class WsLoggerCollector extends DataCollector
{

    private $logger;

    private $modeWebservice;

    public function __construct(WsLogger $logger, bool $isProdMod = false)
    {
        $this->logger = $logger;
        $this->modeWebservice = $isProdMod;
    }


    public function collect(Request $request, Response $response, ?\Throwable $exception = null): void
    {
        $sumTime = 0;
        foreach ($this->logger->getQueries() as $value) {
            $sumTime += $value['time'];
        }

        $this->data = array(
            'urls' => $this->logger->getQueries(),
            'queryCount' => count($this->logger->getQueries()),
            'sumTime' => $sumTime,
            'modeWebservice' => $this->modeWebservice,
        );
    }

    public function reset(): void
    {
        $this->data = [];
    }

    public function getUrls(): array
    {
        return $this->data['urls'];
    }

    public function getQueryCount(): int
    {
        return $this->data['queryCount'];
    }

    public function getSumTime(): int
    {
        return $this->data['sumTime'];
    }

    public function getModeWebservice(): bool
    {
        return $this->data['modeWebservice'];
    }

    public function getName(): string
    {
        return 'app.ws_collector';
    }
}
