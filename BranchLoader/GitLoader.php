<?php

namespace Alienor\SymfonyProfilerExtraGit\BranchLoader;

class GitLoader
{
    private $projectDir;

    public function __construct($projectDir)
    {
        $this->projectDir = $projectDir;
    }

    public function getBranchName($submodule = '')
    {
        if ($submodule !== '') {
            $gitHeadFile = $this->projectDir.'/.git/modules/'.$submodule.'/HEAD';
        } else {
            $gitHeadFile = $this->projectDir.$submodule.'/.git/HEAD';
        }
        $branchname = 'no branch name';
        $stringFromFile = file_exists($gitHeadFile) ? file($gitHeadFile, FILE_USE_INCLUDE_PATH) : "";
        if (isset($stringFromFile) && is_array($stringFromFile)) {
            //get the string from the array
            $firstLine = $stringFromFile[0];
            //seperate out by the "/" in the string
            $explodedString = explode("/", $firstLine, 3);

            if (isset($explodedString[2])) {
                $branchname = trim($explodedString[2]);
            }
        }
        return $branchname;
    }

    public function getLastCommitMessage()
    {
        $gitCommitMessageFile = $this->projectDir.'/.git/COMMIT_EDITMSG';
        $commitMessage = file_exists($gitCommitMessageFile) ? file($gitCommitMessageFile, FILE_USE_INCLUDE_PATH) : "";

        return \is_array($commitMessage) ? trim($commitMessage[0]) : "";
    }

    public function getLastCommitDetail()
    {
        $logs = [];
        $gitLogFile = $this->projectDir.'/.git/logs/HEAD';
        $gitLogs = file_exists($gitLogFile) ? file($gitLogFile, FILE_USE_INCLUDE_PATH) : "";
        $logExploded = (!empty($gitLogs)) ? explode(' ', end($gitLogs)) : "";
        $logs['author'] = $logExploded[2] ?? 'not defined';

        $timestamp = ( is_numeric($logExploded[4]) && (int)$logExploded[4] == $logExploded[4] ) ? $logExploded[4] : $logExploded[5];
        $logs['date'] = (isset($timestamp) && $timestamp != '') ? date('Y/m/d H:i', $timestamp) : "not defined";

        return $logs;
    }

    public function getSubmodulesDetail()
    {
        $gitModulesList = $this->projectDir.'/.gitmodules';
        $gitModules = file_exists($gitModulesList) ? file_get_contents($gitModulesList) : "";

        $re = '/\[submodule "([A-z0-9\/\-_]+)"]/m';
        preg_match_all($re, $gitModules, $matches, PREG_SET_ORDER, 0);

        $submodules = [];
        foreach ($matches as $submodule) {
            $titreSubmodule = explode('/', $submodule[1]);
            $titreSubmodule = array_pop($titreSubmodule);
            $submodules[$titreSubmodule] = $this->getBranchName($submodule[1]);
        }
        return $submodules;
    }
}