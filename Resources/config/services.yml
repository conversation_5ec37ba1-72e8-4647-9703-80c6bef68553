services:
    alienor.gitloader:
        class: 'Alienor\SymfonyProfilerExtraGit\BranchLoader\GitLoader'
        arguments:
            ['%kernel.project_dir%']

    alienor.gitdatacollector:
        class: 'Alienor\SymfonyProfilerExtraGit\DataCollector\GitDataCollector'
        tags:
            -
                name:     data_collector
                template: '@SymfonyProfilerExtraGit/data_collector/git_data.html.twig'
                # must match the value returned by the getName() method
                id: 'app.git_data_collector'
        public: false
        arguments:
            ['@alienor.gitloader']