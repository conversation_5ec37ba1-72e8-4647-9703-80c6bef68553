# LoggerWebserviceBundle

Petit bundle qui permet de logger les appels vers les ws aquitem

```php
<?php

use Alienor\LoggerWebserviceBundle\Logger\WsLogger;

public function loadContent(WsLogger $wsLogger, $url, $paramsRequete) 
{
    $numRequest = $wsLogger->logQueryStart();
    $content = file_get_contents($url);
    $wsLogger->logQueryStop($numRequest, $url, $paramsRequete);
    return $content;
}

```

Si l'autoloader des services est capricieux, il faudra ajouter le binding manuel du service : 

```yaml
services:
    # default configuration for services in *this* file
    _defaults:
        bind:
            $wsLogger: "@alienor.wslogger"
```

# Configuration du bundle (facultatif)

Le seul paramètre permet de définir l'enviromment du webservice

```yaml
alienor_logger_webservice:
    webserviceProd: "%webservice.prod%"
```
