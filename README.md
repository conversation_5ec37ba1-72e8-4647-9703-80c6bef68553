# Symfony Profiler Extra Git

![](screen.png)

## Install
in composer.json
``` json
"require-dev": {
    "alienor/**********************-git": "dev-master"
},
"repositories": [
    {
      "type": "git",
      "url": "**********************:dev-projets-alienor/**********************-git.git"
    }
],
```

in config/bundles.php (4.4 ou flex)
``` php
Alienor\SymfonyProfilerExtraGit\SymfonyProfilerExtraGit::class => ['dev' => true],
```

ou AppKernel.php
``` php
    if (in_array($this->getEnvironment(), ['dev', 'test'], true)) {
        $bundles[] = new Alienor\SymfonyProfilerExtraGit\SymfonyProfilerExtraGit();
    }
``` 

run install commands
```
composer update
php bin/console cache:clear
```