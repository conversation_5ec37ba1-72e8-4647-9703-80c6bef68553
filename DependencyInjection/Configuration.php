<?php

namespace Alienor\RequeteurAdresseBundle\DependencyInjection;

use Symfony\Component\Config\Definition\Builder\TreeBuilder;
use Symfony\Component\Config\Definition\ConfigurationInterface;

/**
 * This is the class that validates and merges configuration from your app/config files
 *
 * To learn more see {@link http://symfony.com/doc/current/cookbook/bundles/extension.html#cookbook-bundles-extension-config-class}
 */
class Configuration implements ConfigurationInterface
{
    /**
     * {@inheritDoc}
     */
    public function getConfigTreeBuilder() :TreeBuilder
    {
        $treeBuilder = new TreeBuilder('alienor_requeteur_adresse');
        $rootNode = $treeBuilder->getRootNode();

        $rootNode
            ->children()
            ->scalarNode('adresse_api_url')->defaultValue('https://api-adresse.data.gouv.fr')->end()
            ->scalarNode('adresse_api_url_commune')->defaultValue('https://geo.api.gouv.fr')->end()
            ->end()
        ;

        return $treeBuilder;
    }
}
