<?php

namespace Alienor\LoggerWebserviceBundle\DependencyInjection;

use Symfony\Component\Config\Definition\Builder\TreeBuilder;
use Symfony\Component\Config\Definition\ConfigurationInterface;

class Configuration implements ConfigurationInterface
{
    public function getConfigTreeBuilder() :TreeBuilder
    {
        $treeBuilder = new TreeBuilder('alienor_loggerWebservice');

        $treeBuilder->getRootNode()
            ->children()
                ->booleanNode('webserviceProd')->defaultFalse()->end()
            ->end()
        ;

        return $treeBuilder;
    }
}