<?php

namespace Alienor\RequeteurAdresseBundle\DependencyInjection;

use Alienor\RequeteurAdresseBundle\Services\AdresseApiConsumer;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\Config\FileLocator;
use Symfony\Component\DependencyInjection\Loader;
use Symfony\Component\HttpKernel\DependencyInjection\Extension;

/**
 * This is the class that loads and manages your bundle configuration
 *
 * To learn more see {@link http://symfony.com/doc/current/cookbook/bundles/extension.html}
 */
class AlienorRequeteurAdresseExtension extends Extension
{
    /**
     * {@inheritDoc}
     */
    public function load(array $configs, ContainerBuilder $container): void
    {
        $configuration = new Configuration();
        $config = $this->processConfiguration($configuration, $configs);

        $loader = new Loader\YamlFileLoader($container, new FileLocator(__DIR__.'/../Resources/config'));
        $loader->load('services.yml');

        $definition = $container->getDefinition(AdresseApiConsumer::class);
        $definition->replaceArgument('$adresse_api_url_commune', $config['adresse_api_url_commune']);
        $definition->replaceArgument('$adresse_api_url', $config['adresse_api_url']);

        $this->addAnnotatedClassesToCompile([
            // you can define the fully qualified class names...
            'Alienor\\RequeteurAdresseBundle\\Controller\\RequeteurAdresseController',
            // ... but glob patterns are also supported:
            '**Bundle\\Controller\\',
            // ...
        ]);
    }
}
