<?php

namespace Alienor\LoggerWebserviceBundle\DependencyInjection;

use Alienor\LoggerWebserviceBundle\DataCollector\WsLoggerCollector;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use <PERSON>ymfony\Component\Config\FileLocator;
use Symfony\Component\DependencyInjection\ChildDefinition;
use Symfony\Component\DependencyInjection\Extension\Extension;
use Symfony\Component\DependencyInjection\Loader\YamlFileLoader;

/**
 * This is the class that loads and manages your bundle configuration
 *
 * To learn more see {@link http://symfony.com/doc/current/cookbook/bundles/extension.html}
 */
class AlienorLoggerWebserviceExtension extends Extension
{
    public function __construct()
    {

    }

    /**
     * {@inheritDoc}
     */
    public function load(array $configs, ContainerBuilder $container): void
    {
        $configuration = new Configuration();
        $config = $this->processConfiguration($configuration, $configs);

        $loader = new YamlFileLoader(
            $container,
            new FileLocator(__DIR__.'/../Resources/config')
        );
        $loader->load('services.yml');
    }
}
