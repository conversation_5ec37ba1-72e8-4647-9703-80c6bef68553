# In all environments, the following files are loaded if they exist,
# the latter taking precedence over the former:
#
#  * .env                contains default values for the environment variables needed by the app
#  * .env.local          uncommitted file with local overrides
#  * .env.$APP_ENV       committed environment-specific defaults
#  * .env.$APP_ENV.local uncommitted environment-specific overrides
#
# Real environment variables win over .env files.
#
# DO NOT DEFINE PRODUCTION SECRETS IN THIS FILE NOR IN ANY OTHER COMMITTED FILES.
# https://symfony.com/doc/current/configuration/secrets.html
#
# Run "composer dump-env prod" to compile .env files for production use (requires symfony/flex >=1.2).
# https://symfony.com/doc/current/best_practices.html#use-environment-variables-for-infrastructure-configuration

###> symfony/framework-bundle ###
APP_ENV=dev
#TRUSTED_PROXIES=*********/8,10.0.0.0/8,**********/12,***********/16
#TRUSTED_HOSTS='^(localhost|example\.com)$'
APP_SECRET=
###< symfony/framework-bundle ###

###> symfony/mailer ###
MAILER_DSN=null://null
###< symfony/mailer ###

###> config ###
salt_client=""
passbook_url="https://wallet.zefid.fr/ws/card/%s/%s/%s"
###< config ###
SYMFONY_TRUSTED_PROXIES="*********/8,10.0.0.0/8,**********/12,***********/16"

ENSEIGNE="normandiepharma"
PROGRAMME_AQUITEM_KEY=""
SALT_CLIENT_CONVERTER=""

###> meteo-concept/hcaptcha-bundle ###
HCAPTCHA_SITE_KEY="10000000-ffff-ffff-ffff-000000000001"
HCAPTCHA_SECRET="******************************************"
###< meteo-concept/hcaptcha-bundle ###

COMPOSE_PROJECT_NAME=zefid_portail_client

IDE_CONFIG="phpstorm://open?file=%f&line=%l&/var/www/html/>/home/<USER>/docker/zefid-client-craftmanship/"

#TODO : remove localhost (sans sous domaine) une fois que les enseignes seront bien configurées
SERVER_NAME="${ENSEIGNE}.localhost, localhost"
TASK_X_REMOTE_TASKFILES=1
SITE_BASE_SCHEME=https
SITE_BASE_HOST="${ENSEIGNE}.localhost"
MAILER_SENDER=<EMAIL>
MAILER_DEV_RECIPIENT='<EMAIL>'

###> DON ###
AQUITEM_DONCHEQUE_TOKEN_ALIENOR=
AQUITEM_DONCHEQUE_TOKEN_AQUITEM=
###< DON ###
